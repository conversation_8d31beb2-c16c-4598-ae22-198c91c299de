// 导入获取billNo相关hook
import { useCheckBillNoCache } from "@/utils/hooks";
// message组件
import { ElMessageBox } from "element-plus";
import { message } from "@/utils/message";
// xlsx导出
import exportToExcel from "@/utils/exportXlsx";
// 导入公用方法
import {
  getSaleOrderSequence,
  updateSaleOrderSequence
} from "@/api/full-lifecycle/fullLifecycle";

// 映射方法
import { mapHandle } from "./mapHandle";
import { ref } from "vue";
// 求和hook
import { useColumnsTotal } from "@/views/full-lifecycle/utils";
import { cloneDeep } from "lodash";

/**
 * @function 获取表格数据相关hook
 * @param {String} routeListId 路由id
 * @param tableRef 表格ref
 * @param queryData 普通查询，高级筛选查询参数
 * @param tableQuery 表格内的查询参数
 * @param selectRows 表格内选中的行
 * @returns {Function} 相关查询函数
 */
export const useFetchTableData = (
  routeListId,
  tableRef,
  queryData,
  tableQuery,
  selectRows
) => {
  /** 用于重置，在初始化时获取query初始值 */
  const resetQuery = cloneDeep(queryData.value); // 深拷贝取值

  /** 隐藏列数据 */
  const hiddenColumns = ref([]);
  const { nodeStore, checkBillNoCache } = useCheckBillNoCache();
  /**
   * @function getFetchTableDataMethod 定义获取表格数据的方法
   * @param Function getSaleOrder 获取数据API
   * @param Object statesConfig 修改映射值的对象
   *   - **states** `{Array}`: 映射的枚举数组
   *   - **addPercentSigns** `{Array}`: 需要添加%的字段数组
   *   - **sortFields** `{Array}`: 排序的字段数组
   *   - **isExpand** `Boolean`: 是否需要展开数据
   * @param Boolean localQueryable 是否使用本地存储的获取数据
   * @param Object queryParams 不使用本地存储，传入请求参数
   * @param Boolean home  是否是home节点
   * @param Boolean isExpand  是否需要展开数据
   * @return  Function - fetchTableData 获取表格数据方法
   */
  const getFetchTableDataMethod = (
    getSaleOrder,
    statesConfig,
    localQueryAble = true,
    queryParams = {},
    home = false
  ) => {
    const fetchTableData = async () => {
      tableRef.value?.loading(true);
      try {
        // 请求id
        let req = "" || {};
        /**@description 判断是否使用本地存储的queryId */
        if (localQueryAble) {
          // 查询前获取缓存中的数据
          req = checkBillNoCache(home);
        } else {
          req = queryParams;
        }

        // 获取请求返回数据
        let response = [];

        if (home) {
          // home与子节点传参也不一样，需要分开处理
          response = await getSaleOrder({
            page: { ...queryData.value.page },
            query: [...queryData.value.query, ...tableQuery.value],
            order: [...queryData.value.order]
          });

          /** 当本地未存储query,默认取首页列表第一条的销售订单号存储到本地 */
          if (!nodeStore.queryId) {
            nodeStore.setQueryId(response.data.records[0].sytText1);
            nodeStore.setSaleOrderId(response.data.records[0].billNo);
          }
        } else {
          response = await getSaleOrder(
            {
              page: { ...queryData.value.page },
              query: [...queryData.value.query, ...tableQuery.value],
              order: [...queryData.value.order]
            },
            req
          );
          // console.log(response, "response-1");
        }
        // 处理映射修改 todo
        let records = await mapHandle(response.data.records, statesConfig);
        // console.log(records, "records");

        if (statesConfig.isExpand) {
          // console.log("需要展开");

          // 设置父子对应字段格式
          records = records.map(item => {
            return { ...item, parentCode: null };
          });

          // 获取拿子数据的参数
          const queryParams = records.map(item => item.materialCode);

          // 将所有的ID发给后端获取全部子数据 请求列表
          const { saleOrderArrResult, saleOrderArrResultTotal } =
            await statesConfig.handleExtractIds(queryParams);
          // console.log(saleOrderArrResult, "saleOrderArrResult");
          // console.log(saleOrderDetailKeysResult, "后端字段属性");

          response.data.total =
            response.data.total || saleOrderArrResultTotal || 0;

          const expand =
            queryParams.length > 0
              ? await replaceBillNoWithParentBillNo(saleOrderArrResult)
              : saleOrderArrResult;
          if (queryParams.length == 0) {
            columns.value = columns.value.map(item => {
              item.visible = true;
              return item;
            });
          }
          // console.log(aa, "后端子数据aa");
          // 将二维数组转换为对象数组
          // const flattenedArray = aa.flatMap(arr => arr);

          // records = await [
          //   ...records,
          //   { parentBillNo: "XSDD000026", billNo: "1355" }
          // ];
          records = await [...records, ...expand];
        } else {
          console.log("不需要展开");
        }
        console.log(records, "records");

        return {
          records,
          total: response.data.total,
          code: 200
        };
      } catch (error) {
        console.error("Error fetching table data:", error);
        return { records: [], total: 0, code: 500 };
      }
    };

    return fetchTableData;
  };

  /**
   * 将数据中的 billNo 字段名替换为 parentBillNo
   * @param {Array} data - 需要处理的数据数组
   * @returns {Array} - 处理后的数据数组
   */
  function replaceBillNoWithParentBillNo(data2) {
    const expand = data2.map(item => {
      const parentCode = item.materialCode;
      delete item.materialCode;

      // console.log(parentCode);

      return { ...item, parentCode };
    });
    return expand;
  }

  const columns = ref([]);
  const historyColumnsKeys = ref([]);
  /**
   * @function 获取销售订单表头字段排序信息
   * @param getPageDetailKeysAPI 获取页面字段信息API
   * @param columns 列设置列表
   */
  const getSortedColumnHeaders = (
    getPageDetailKeysAPI,
    columns,
    getTableIdAPI = null,
    specialId = null
  ) => {
    // 特殊处理-获取销售订单表头字段排序信息
    // 判断路由是否特殊处理
    if (getTableIdAPI && specialId) {
      getTableIdAPI(specialId).then(async res => {
        historyColumnsKeys.value = (await res.data) || [];
        if (res.code != 200) {
          //如果没有历史记录 则执行直接赋值操作 给默认值操作
          const fieldRes = await getPageDetailKeysAPI();
          // console.log("获取后端定义的字段:", columns);
          let columnsDefault = await mapFields(fieldRes.data);
          columns.value = await columnsDefault;
          // console.log("前端处理映射后11:", columns.value);
        } else {
          // 如果有历史记录则 将后端字段 和 历史字段 进行匹配，主要作用 历史记录匹配到后端字段则进行重新赋值参数、否则将未匹配的后端字段设置默认值
          await initializeColumns(
            getPageDetailKeysAPI,
            historyColumnsKeys
          ).then(updatedColumns => {
            columns.value = updatedColumns;
          });
        }
      });
    } else {
      //获取销售订单表头字段排序信息
      getSaleOrderSequence(routeListId).then(async res => {
        historyColumnsKeys.value = (await res.data) || [];
        if (res.code != 200) {
          //如果没有历史记录 则执行直接赋值操作 给默认值操作
          const fieldRes = await getPageDetailKeysAPI();
          // console.log("获取后端定义的字段:", columns);
          let columnsDefault = await mapFields(fieldRes.data);
          columns.value = await columnsDefault;
          // console.log("前端处理映射后11:", columns.value);
        } else {
          // 如果有历史记录则 将后端字段 和 历史字段 进行匹配，主要作用 历史记录匹配到后端字段则进行重新赋值参数、否则将未匹配的后端字段设置默认值
          await initializeColumns(
            getPageDetailKeysAPI,
            historyColumnsKeys
          ).then(updatedColumns => {
            columns.value = updatedColumns;

            // 请求两个字段拼接

            // if (statesConfig.isExpand) {
            //   columns.value.push({
            //     fixed: false,
            //     label: "某",
            //     prop: "ownerId",
            //     type: "varchar(255)",
            //     visible: true,
            //     width: 200
            //   });
            // }
            console.log("Final Columns:", columns.value);
          });
        }
      });
    }
  };

  /**
   * @function  获取更新保存列设置方法
   * @param columns 列设置列表
   */
  const updateColumns = async columns => {
    const newColumns = [...columns];
    columns.value = newColumns;
    // console.log("列设置更新:", columns);
    updateSaleOrderSequence({
      tableId: routeListId,
      tableColumns: columns.value
    });
  };

  /**
   * @function 修改查询参数的方法
   * @param newData 更新查询条件
   */
  const updateQueryDate = newData => {
    tableQuery.value = newData;
    // console.log(queryData.value, "query");
  };

  /**
   * @function 获取勾选的数据
   * @param rows 勾选数据列表
   */
  const handleSelectedRows = rows => {
    selectRows.value = rows;
  };

  /**
   * @function 编辑方法
   */
  const handleEdit = async (
    handle = "edit",
    statusMethod = null,
    idsKey = "id"
  ) => {
    // 判断勾选值长度，超过报错
    if (selectRows.value.length > 0) {
      if (handle == "status") {
        const allResolve = selectRows.value.some(
          row => row.resolved === "已处理"
        );

        if (allResolve) {
          message("所选数据包含已处理的数据，请重新选择", { type: "error" });
          return;
        }
        const ids = selectRows.value.map(item => item[idsKey]);
        const res = await statusMethod({ ids });
        // const res = await updateAbnormalSituationStatusAPI({ ids });
        message(res.msg, { type: res.code == 200 ? "success" : "error" });
        tableRef.value.fetchTableData();
        selectRows.value = [];
        return;
      } else if (handle == "editTime") {
        if (selectRows.value.length > 1) {
          message("只能编辑一条数据", { type: "error" });
          return;
        }
      } else if (handle == "UploadThirdPartyInspection") {
        ElMessageBox.confirm("是否上传第三方报告？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(async () => {
          const ids = selectRows.value.map(item => item[idsKey]);
          const res = await statusMethod({ ids });
          message(res.msg, { type: res.code == 200 ? "success" : "error" });
          tableRef.value.fetchTableData();
          selectRows.value = [];
        });
        return;
      } else if (handle == "distribution") {
        // 检查所有选中行的 errType 是否一致
        const firstErrType = selectRows.value[0].errType;
        console.debug("selectRows", selectRows.value);
        const allSameErrType = selectRows.value.every(
          row => row.errType === firstErrType
        );

        const allResolve = selectRows.value.some(
          row => row.resolved === "已处理"
        );

        if (allResolve) {
          message("所选数据包含已处理的数据，请重新选择", { type: "error" });
          return;
        }

        if (!allSameErrType) {
          message("所选数据的异常类型不一致，请重新选择", { type: "error" });
          return;
        }
      }

      tableRef.value.handleOpenEdit(handle, selectRows.value);
      selectRows.value = [];
    } else {
      message("请勾选所需要处理的数据", { type: "error" });
    }
  };

  /**
   * @function 导出方法-判断用户是勾选导出还是全部导出
   */
  const handleExportExcel = (exportAllAPI, queryParams = {}) => {
    // console.log(selectRows, "selectRows");

    ElMessageBox.confirm(
      `确认要导出<strong>${selectRows.value.length > 0 ? "所勾选的数据" : "全部数据"}</strong><strong style='color:var(--el-color-primary)'></strong>吗？`,
      "系统提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true,
        draggable: true
      }
    )
      .then(async () => {
        if (selectRows.value.length > 0) {
          exportToExcel(columns.value, selectRows.value, "表格数据");
          return message("勾选数据导出成功", {
            type: "success"
          });
        } else {
          const fields = columns.value.map(item => item.prop);
          console.log("queryParams", queryParams);

          // 请求后端接口
          const res = await exportAllAPI(
            queryParams,
            { ...queryData.value, page: { size: -1, current: 1 } },
            fields
          );

          // console.log(res);
          let fileName = "downloaded_file"; // 默认文件名
          let disposition = res.headers["content-disposition"];
          if (disposition) {
            const match = disposition.match(/filename=(.+).xlsx/);
            console.log(disposition);
            if (match) {
              fileName = decodeURIComponent(match[1]); // 处理 UTF-8 编码的文件名
            } else {
              // 兼容 filename="xxx.pdf" 格式
              const match2 = disposition.match(/filename="(.+?)"/);
              if (match2) {
                fileName = match2[1];
              }
            }
          }

          fileName = fileName.slice(1);

          const url = window.URL.createObjectURL(new Blob([res.data]));
          const link = document.createElement("a");
          link.href = url;
          link.setAttribute("download", `${fileName}.csv`); // 设置下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);

          return message("筛选数据导出成功", {
            type: "success"
          });
        }
      })
      .catch(() => {});
  };

  /**
   * @function 搜索方法-根据筛选条件获取数据
   * @param filter 筛选条件
   */
  const InputSearchUpdateFilter = filter => {
    // console.log(filter, "筛选条件");
    queryData.value.query = filter;

    tableRef.value.fetchTableData();
    tableRef.value.resetPagination();
  };

  /**
   * @function 重置筛选条件
   */
  const InputSearchResetFilter = () => {
    // 重置筛选条件
    console.log("重置筛选条件");
    // 通知table子组件重置搜索框
    tableRef.value?.resetFilter();
    tableRef.value.resetPagination();
    tableQuery.value = [];

    // 清空查询条件，只保留分页和排序信息
    const pageInfo = cloneDeep(queryData.value.page);
    const orderInfo = cloneDeep(queryData.value.order);

    // 创建一个新的查询对象，只包含分页和排序信息，不包含查询条件
    queryData.value = {
      page: pageInfo,
      query: [], // 清空查询条件
      order: orderInfo
    };

    console.log("重置后的queryData:", queryData.value);

    // 重新获取数据
    tableRef.value.fetchTableData();
  };

  /** 用于存储第一次请求后获取的total列表 */
  let totalColumnList = {};
  /**
   * @function 获取求和方法
   * @param sumFieldList 求和字段数组
   * @param getColumnTotalAPi 求和API
   * @param totalQuery 求和参数
   * @returns 求和列表
   */
  const getTotalHook = (sumFieldList, getColumnTotalAPi, totalQuery) => {
    // 获取所有总计信息
    const getTotal = async () => {
      // 调用hook(求和字段数组，请求，查询参数)
      totalColumnList = await useColumnsTotal(
        sumFieldList.value,
        getColumnTotalAPi,
        [...queryData.value.query, ...tableQuery.value],
        { ...totalQuery }
      );
      return totalColumnList;
    };
    return getTotal;
  };

  /**
   * @function 更新页码
   * @param val 新页码
   */
  const handleCurrentPageChange = async val => {
    queryData.value.page.current = val;
    // console.log(`${val} items per page`);
  };

  /**
   * @function 更新每页显示条数
   * @param val 每页显示条数
   */
  const handlePageSizeChange = async val => {
    queryData.value.page.size = val;
    // await fetchTableData(currentPage.value, pageSize.value);
    // console.log(`${val} items per size`);
  };

  return {
    getFetchTableDataMethod,
    getSortedColumnHeaders,
    updateColumns,
    handleExportExcel,
    InputSearchUpdateFilter,
    InputSearchResetFilter,
    updateQueryDate,
    handleSelectedRows,
    handleEdit,
    getTotalHook,
    handleCurrentPageChange,
    handlePageSizeChange,
    columns,
    resetQuery,
    hiddenColumns
  };
};

/**
 * @description  后端字段映射关系处理
 * @param data 需要处理的数据
 * @returns
 */
async function mapFields(data) {
  // type为空判断， 正则匹配，匹配字段名以Date结尾的改为datetime类型，其余默认为varchar(255)
  const regex = /.*Date$/;
  data = data.map(item => {
    // 交期异常备注特殊处理成可编辑字段类型
    if (item.field === "deliveryAbnormalRemarks") {
      item.type = "textarea";
    }
    if (item.field === "errType") {
      item.type = "enum('采购异常','生产异常','销售异常')";
    }
    if (item.type) {
    } else {
      item.type = regex.test(item.field) ? "datetime" : "varchar(255)";
    }
    return item;
  });

  return await data.map(item => ({
    prop: item.field,
    label: item.description,
    visible: true,
    width: 200,
    fixed: false,
    type: item.type ? item.type : "varchar(255)"
  }));
}

/**
 * @description  更新字段信息的函数
 * @param Function
 * @returns
 */
async function initializeColumns(getPageDetailKeysAPI, historyColumnsKeys) {
  try {
    // 获取销售订单字段信息并映射
    const fieldRes = await getPageDetailKeysAPI();
    // console.log("获取后端定义的字段:", fieldRes.data);
    let columns = await mapFields(fieldRes.data);
    // console.log(columns, "前端处理映射后:");
    // console.log(historyColumnsKeys.value, "历史列设置字段信息");

    // 创建映射表，用于快速查找并替换匹配的字段信息
    const columnsMap = new Map();
    columns.forEach(column => {
      columnsMap.set(column.prop, column);
    });

    // 关键一步：匹配并替换字段，并按照 historyColumnsKeys 的顺序进行排序
    const updatedColumns = historyColumnsKeys.value
      .map(historyCol => {
        const matchingColumn = columnsMap.get(historyCol.prop);
        if (matchingColumn) {
          // console.log(matchingColumn, "匹配到的列信息");

          // 只替换匹配到的特定字段
          return {
            ...matchingColumn,
            visible: historyCol.visible,
            width: historyCol.width,
            fixed: historyCol.fixed
          };
        } else {
          // console.log(`未匹配到 ${historyCol.prop} 的列，保持原始信息`);
          return null;
        }
      })
      .filter(Boolean); // 过滤掉未匹配到的列

    // 将 columns 中未匹配的列添加到 updatedColumns 中
    columns.forEach(column => {
      if (!updatedColumns.some(updatedCol => updatedCol.prop === column.prop)) {
        updatedColumns.push(column);
      }
    });

    // console.log(updatedColumns, "更新后的列");

    return updatedColumns;
  } catch (error) {
    console.error("Error initializing columns:", error);
  }
}

/**
 * @function 获取可展开表格数据相关hook
 * @param {String} routeListId 路由id
 * @param tableRef 表格ref
 * @param queryData 普通查询，高级筛选查询参数
 * @param tableQuery 表格内的查询参数
 * @param selectRows 表格内选中的行
 * @param dateQueryData 日期查询参数
 * @returns {Function} 相关查询函数
 */
export const useFetchDBTableData = (
  routeListId,
  tableRef,
  queryData,
  tableQuery,
  selectRows,
  dateQueryData,
  isExpand
) => {
  /** 用于重置，在初始化时获取query初始值 */
  const resetQuery = cloneDeep(queryData.value); // 深拷贝取值

  /** 隐藏列数据 */
  const hiddenColumns = ref([]);

  /**
   * @function getFetchTableDataMethod 定义获取表格数据的方法
   * @param Function getSaleOrder 获取数据API
   * @param Object statesConfig 修改映射值的对象
   *   - **states** `{Array}`: 映射的枚举数组
   *   - **addPercentSigns** `{Array}`: 需要添加%的字段数组
   *   - **sortFields** `{Array}`: 排序的字段数组
   *   - **isExpand** `Boolean`: 是否需要展开数据
   * @param Boolean localQueryable 是否使用本地存储的获取数据
   * @param Object queryParams 不使用本地存储，传入请求参数
   * @param Boolean home  是否是home节点
   * @param Boolean isExpand  是否需要展开数据
   * @return  Function - fetchTableData 获取表格数据方法
   */
  const getFetchTableDataMethod = (getSaleOrder, statesConfig) => {
    const fetchTableData = async () => {
      tableRef.value?.loading(true);
      try {
        // console.log(dateQueryData, "dateFet");

        // 获取请求返回数据
        let response = [];

        // 展开table时传入日期参数
        response = await getSaleOrder(
          {
            page: { ...queryData.value.page },
            query: [...queryData.value.query, ...tableQuery.value],
            order: [...queryData.value.order]
          },
          dateQueryData.value
        );
        // console.log(response, "response-1");
        // 处理映射修改
        let records = await mapHandle(response?.data?.records, statesConfig);
        // console.log(records, "records");

        // 处理展开数据
        if (statesConfig.isExpand) {
          // 设置父对应字段格式,添加对应的parentCode,这里的处理不用赋值,但必须有这个对应字段
          records = records.map(item => {
            return { ...item, parentCode: null };
          });

          // 从父数据获取对应的id数组,用于获取每个id的子数据
          const queryParams = records.map(
            item => item[statesConfig.threeConfig.value.rowField]
          );

          // 将对应的父数据ID数组发给后端获取全部子数据
          const { saleOrderArrResult, saleOrderArrResultTotal } =
            await statesConfig.handleExtractIds(
              queryParams,
              dateQueryData.value,
              statesConfig.threeConfig.value.rowField
            );

          // 判断是否有父数据,没有则显示子数据的total
          response.data.total =
            response.data.total || saleOrderArrResultTotal || 0;

          // 如果父数据存在,则将子数据对应渲染为树形展开结构,不存在直接渲染子数据
          const expand =
            queryParams.length > 0
              ? await transformDataToTreeStructure(
                  saleOrderArrResult,
                  statesConfig.threeConfig.value.rowField
                )
              : saleOrderArrResult;
          if (queryParams.length == 0) {
            columns.value = columns.value.map(item => {
              item.visible = true;
              return item;
            });
          }
          // console.log(aa, "后端子数据aa");
          // 将二维数组转换为对象数组
          // const flattenedArray = aa.flatMap(arr => arr);

          // records = await [
          //   ...records,
          //   { parentBillNo: "XSDD000026", billNo: "1355" }
          // ];
          records = await [...records, ...expand];
        } else {
          console.log("不需要展开");
        }
        console.log(records, "records");

        return {
          records,
          total: response.data.total,
          code: 200
        };
      } catch (error) {
        console.error("Error fetching table data:", error);
        return { records: [], total: 0, code: 500 };
      }
    };

    return fetchTableData;
  };

  /**
   * 将数据处理为树形展开格式
   * @param {Array} data - 需要处理的数据数组
   * @param {String} name - 需要过滤的字段名
   * @returns {Array} - 处理后的数据数组
   */
  function transformDataToTreeStructure(data2, name) {
    const expand = data2.map(item => {
      const parentCode = item[name];

      // 使用 Object.entries 过滤掉指定字段
      const newItem = Object.fromEntries(
        Object.entries(item).filter(([key]) => key !== name) // 过滤掉要排除的字段
      );

      return { ...newItem, parentCode };
    });

    return expand;
  }

  const columns = ref([]);
  const historyColumnsKeys = ref([]);

  /**
   * @function 获取可展开table销售订单表头字段排序信息
   * @param getPageDetailKeysAPI1 获取父级字段信息API
   * @param getPageDetailKeysAPI1 获取子级字段信息API
   * @param columns 列设置列表
   */
  const getSortedDBColumnHeaders = (
    getPageDetailKeysAPI1,
    getPageDetailKeysAPI12,
    columns
  ) => {
    //获取销售订单表头字段排序信息
    getSaleOrderSequence(routeListId).then(async res => {
      historyColumnsKeys.value = (await res.data) || [];
      if (res.code != 200) {
        //如果没有历史记录 则执行直接赋值操作 给默认值操作
        const fieldRes1 = await getPageDetailKeysAPI1();
        const fieldRes2 = await getPageDetailKeysAPI12();

        // 获取子数据需要隐藏的字段
        const filterColumns = fieldRes2.data.filter(item => {
          return !fieldRes1.data.some(it => it.field === item.field);
        });
        hiddenColumns.value = filterColumns;

        let columnsDefault = await mapDBFields(fieldRes1.data, filterColumns);

        // console.log(filterColumns, "数组");

        columns.value = await columnsDefault;
        // console.log("Final Columns1:", columns.value);
      } else {
        // 如果有历史记录则 将后端字段 和 历史字段 进行匹配，主要作用 历史记录匹配到后端字段则进行重新赋值参数、否则将未匹配的后端字段设置默认值
        await DBinitializeColumns(
          getPageDetailKeysAPI1,
          getPageDetailKeysAPI12,
          historyColumnsKeys,
          hiddenColumns
        ).then(updatedColumns => {
          columns.value = updatedColumns;
        });
      }
    });
  };

  /**
   * @function  获取更新保存列设置方法
   * @param columns 列设置列表
   */
  const updateColumns = async columns => {
    const newColumns = [...columns];
    columns.value = newColumns;
    console.log("列设置更新:", columns);
    updateSaleOrderSequence({
      tableId: routeListId,
      tableColumns: columns.value
    });
  };

  /**
   * @function 修改查询参数的方法
   * @param newData 更新查询条件
   */
  const updateQueryDate = newData => {
    tableQuery.value = newData;
    // console.log(queryData.value, "query");
  };

  /**
   * @function 获取勾选的数据
   * @param rows 勾选数据列表
   */
  const handleSelectedRows = rows => {
    selectRows.value = rows;
  };

  /**
   * @function 编辑方法
   */
  const handleEdit = () => {
    // 判断勾选值长度，超过报错
    if (selectRows.value.length > 0) {
      tableRef.value.handleOpenEdit(selectRows.value);
    } else {
      message("请勾选所需要添加备注的数据", { type: "error" });
    }
  };

  /**
   * @function 导出方法-判断用户是勾选导出还是全部导出
   */
  const handleExportExcel = (exportAllAPI, queryParams = {}) => {
    // console.log(selectRows, "selectRows");

    ElMessageBox.confirm(
      `确认要导出<strong>${selectRows.value.length > 0 ? "所勾选的数据" : "所筛选的数据"}</strong><strong style='color:var(--el-color-primary)'></strong>吗？`,
      "系统提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        dangerouslyUseHTMLString: true,
        draggable: true
      }
    )
      .then(async () => {
        if (selectRows.value.length > 0) {
          exportToExcel(columns.value, selectRows.value, "表格数据");
          return message("勾选数据导出成功", {
            type: "success"
          });
        } else {
          const fields = columns.value.map(item => item.prop);

          // 请求后端接口
          const res = await exportAllAPI(
            queryParams,
            { ...queryData.value, page: { size: -1, current: 1 } },
            fields
          );

          // console.log(res);
          let fileName = "downloaded_file"; // 默认文件名
          let disposition = res.headers["content-disposition"];
          if (disposition) {
            const match = disposition.match(/filename=(.+).xlsx/);
            console.log(disposition);
            if (match) {
              fileName = decodeURIComponent(match[1]); // 处理 UTF-8 编码的文件名
            } else {
              // 兼容 filename="xxx.pdf" 格式
              const match2 = disposition.match(/filename="(.+?)"/);
              if (match2) {
                fileName = match2[1];
              }
            }
          }

          const url = window.URL.createObjectURL(new Blob([res.data]));
          const link = document.createElement("a");
          link.href = url;
          link.setAttribute("download", `${fileName}.csv`); // 设置下载的文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);

          return message("筛选数据导出成功", {
            type: "success"
          });
        }
      })
      .catch(() => {});
  };

  /**
   * @function 搜索方法-根据筛选条件获取数据
   * @param filter 筛选条件
   */
  const InputSearchUpdateFilter = filter => {
    // console.log(filter, "筛选条件");
    queryData.value.query = filter;

    tableRef.value.fetchTableData();
    tableRef.value.resetPagination();
  };

  /**
   * @function 重置筛选条件
   */
  const InputSearchResetFilter = () => {
    // 重置筛选条件
    console.log("重置筛选条件");
    // 通知table子组件重置搜索框
    tableRef.value?.resetFilter();
    tableRef.value.resetPagination();
    tableQuery.value = [];

    // 清空查询条件，只保留分页和排序信息
    const pageInfo = cloneDeep(queryData.value.page);
    const orderInfo = cloneDeep(queryData.value.order);

    // 创建一个新的查询对象，只包含分页和排序信息，不包含查询条件
    queryData.value = {
      page: pageInfo,
      query: [], // 清空查询条件
      order: orderInfo
    };

    console.log("重置后的queryData:", queryData.value);

    // 重新获取数据
    tableRef.value.fetchTableData();
  };

  /** 用于存储第一次请求后获取的total列表 */
  let totalColumnList = {};
  /**
   * @function 获取求和方法
   * @param sumFieldList 求和字段数组
   * @param getColumnTotalAPi 求和API
   * @param totalQuery 求和参数
   * @returns 求和列表
   */
  const getTotalHook = (sumFieldList, getColumnTotalAPi, totalQuery) => {
    // 获取所有总计信息
    const getTotal = async () => {
      // 调用hook(求和字段数组，请求，查询参数)
      if (Object.keys(totalColumnList).length === 0) {
        totalColumnList = await useColumnsTotal(
          sumFieldList.value,
          getColumnTotalAPi,
          [...queryData.value.query, ...tableQuery.value],
          { ...totalQuery }
        );
        // console.log(totalColumnList, "totalColumnList");
      }
      return totalColumnList;
    };
    return getTotal;
  };

  /**
   * @function 更新页码
   * @param val 新页码
   */
  const handleCurrentPageChange = async val => {
    isExpand.value = false;
    columns.value = columns.value.map(item => {
      // 如果 item.field 存在于 hiddenColumns 中，则设置 visible 属性
      if (hiddenColumns.value.some(it => it.field == item.prop)) {
        item.visible = !item.visible; // 设置可见性
      }
      return item; // 返回修改后的对象
    });
    queryData.value.page.current = val;
    // console.log(`${val} items per page`);
  };

  /**
   * @function 更新每页显示条数
   * @param val 每页显示条数
   */
  const handlePageSizeChange = async val => {
    isExpand.value = false;
    columns.value = columns.value.map(item => {
      // 如果 item.field 存在于 hiddenColumns 中，则设置 visible 属性
      if (hiddenColumns.value.some(it => it.field == item.prop)) {
        item.visible = !item.visible; // 设置可见性
      }
      return item; // 返回修改后的对象
    });
    queryData.value.page.size = val;
    // await fetchTableData(currentPage.value, pageSize.value);
    // console.log(`${val} items per size`);
  };

  return {
    getFetchTableDataMethod,
    getSortedDBColumnHeaders,
    updateColumns,
    handleExportExcel,
    InputSearchUpdateFilter,
    InputSearchResetFilter,
    updateQueryDate,
    handleSelectedRows,
    handleEdit,
    getTotalHook,
    handleCurrentPageChange,
    handlePageSizeChange,
    columns,
    resetQuery,
    hiddenColumns
  };
};

/**
 * @description  可展开table有历史记录字段获取方法
 * @param Function1 获取父级字段信息
 * @param Function2 获取子级字段信息
 * @param historyColumnsKeys 历史保存字段信息
 * @param hiddenColumns 需要隐藏的字段信息
 * @returns
 */
async function DBinitializeColumns(
  getPageDetailKeysAPI1,
  getPageDetailKeysAPI12,
  historyColumnsKeys,
  hiddenColumns
) {
  try {
    // 获取销售订单字段信息并映射
    //如果没有历史记录 则执行直接赋值操作 给默认值操作
    const fieldRes1 = await getPageDetailKeysAPI1();
    const fieldRes2 = await getPageDetailKeysAPI12();
    // 子字段需要隐藏的字段
    const filterColumns = fieldRes2.data.filter(item => {
      // 检查 item.filed 是否在 fieldRes1.data 中已存在
      return !fieldRes1.data.some(it => it.field === item.field);
    });
    hiddenColumns.value = filterColumns;
    // console.log(uniqueArray, "数组");

    // 最终处理去重后的数组
    let columns = await mapDBFields(fieldRes1.data, filterColumns);

    console.log(columns, "前端处理映射后:");
    // console.log(historyColumnsKeys.value, "历史列设置字段信息");

    // 创建映射表，用于快速查找并替换匹配的字段信息
    const columnsMap = new Map();
    columns.forEach(column => {
      columnsMap.set(column.prop, column);
    });

    // 关键一步：匹配并替换字段，并按照 historyColumnsKeys 的顺序进行排序
    const updatedColumns = historyColumnsKeys.value
      .map(historyCol => {
        const matchingColumn = columnsMap.get(historyCol.prop);
        if (matchingColumn) {
          // console.log(matchingColumn, "匹配到的列信息");

          // 只替换匹配到的特定字段
          return {
            ...matchingColumn,
            visible: historyCol.visible,
            width: historyCol.width,
            fixed: historyCol.fixed
          };
        } else {
          // console.log(`未匹配到 ${historyCol.prop} 的列，保持原始信息`);
          return null;
        }
      })
      .filter(Boolean); // 过滤掉未匹配到的列

    // 将 columns 中未匹配的列添加到 updatedColumns 中
    columns.forEach(column => {
      if (!updatedColumns.some(updatedCol => updatedCol.prop === column.prop)) {
        updatedColumns.push(column);
      }
    });

    // console.log(updatedColumns, "更新后的列");

    return updatedColumns;
  } catch (error) {
    console.error("Error initializing columns:", error);
  }
}
/**
 * @description  可展开table后端字段映射关系处理
 * @param data1 需要处理的父字段
 * @param data2 需要处理的子字段
 * @returns 处理后合并的字段
 */
async function mapDBFields(data1, data2) {
  const regex = /.*Date$/;

  const val1 = await data1.map(item => ({
    prop: item.field,
    label: item.description,
    visible: true,
    width: 200,
    fixed: false,
    type: regex.test(item.type) ? "datetime" : "varchar(255)"
  }));

  const val2 = await data2.map(item => ({
    prop: item.field,
    label: item.description,
    visible: false,
    width: 200,
    fixed: false,
    type: regex.test(item.type) ? "datetime" : "varchar(255)"
  }));
  // console.log(val1, val2, "数组");

  return [...val1, ...val2];
}
