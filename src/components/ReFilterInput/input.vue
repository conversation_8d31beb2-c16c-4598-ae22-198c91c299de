<template>
  <div class="filter-component">
    <div>
      <div class="flex items-center mb-1">
        <div class="text-sm">我的方案</div>
        <div>
          <el-check-tag
            v-for="item in customFiltersPlan"
            :key="item.schemeName"
            class="mr-2 relative"
            :style="item.schemeName === '默认方案' ? '' : 'padding-right: 30px'"
            :checked="item.checked"
            @click="handleFilterPlanChange(item)"
            >{{ item.schemeName }}
            <el-button
              v-if="item.schemeName !== '默认方案'"
              class="absolute top-1 right-1"
              type="danger"
              :icon="Close"
              link
              @click.stop="handleDeleteUserFilterPlan(item)"
            />
          </el-check-tag>
        </div>
      </div>
      <div class="flex items-center">
        <div class="text-sm">快捷过滤</div>
        <customFilterInput
          v-for="(item, index) in currentFilterPlan.schemeContent"
          :key="index"
          :ref="
            el => {
              if (el) {
                filterInputs[index] = el;
              }
            }
          "
          :columns="columns"
          :filterData="item"
          :enterFilter="applyFilter"
          :mappingStatus="mappingStatus"
        />

        <!-- 快捷筛选 -->
        <el-select
          v-if="fastFilter"
          v-model="fastSelVal"
          :placeholder="fastFilter.placeholder"
          style="width: 140px"
          clearable
          @change="applyFilter"
        >
          <el-option
            v-for="item in fastFilter.options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>

        <div class="filter-actions">
          <el-button
            type="primary"
            size="small"
            class="min-h-6 min-w-20"
            :icon="Search"
            @click="applyFilter"
          >
            {{ $t("buttons.pureSearch") }}
          </el-button>
          <el-button
            type="default"
            size="small"
            :icon="Refresh"
            class="min-h-6 min-w-20"
            @click="resetFilter"
          >
            {{ $t("buttons.pureReset") }}
          </el-button>
          <el-tooltip content="筛选方案配置" placement="top">
            <el-icon class="custom-tooltip" @click="showQualityFilter">
              <Tools />
            </el-icon>
          </el-tooltip>
          <el-button
            v-if="showSaveUserFilterPlan"
            type="primary"
            link
            @click="handleAddUserFilterPlan"
          >
            保存方案
          </el-button>
          <ButtonGroup :buttons="topBtns" class="isFlex-default-none" />
        </div>
      </div>
    </div>
    <ButtonGroup :buttons="topBtns" class="isFlex shrink-0" />
    <QualityInput
      ref="QualityFilterRef"
      :columns="columns"
      :mappingStatus="mappingStatus"
      @update:visible="handleQualityFilter"
    />
    <!--  保存用户筛选方案弹窗  -->
    <el-dialog
      v-model="isShowSaveUserFilterPlan"
      title="自定义筛选方案"
      width="560px"
      draggable
    >
      <el-form ref="filterPlanForm" label-width="100px">
        <el-form-item label="方案名称">
          <el-input
            v-model="customFilterPlanName"
            style="width: 200px"
            placeholder="请输入方案名称"
            required
          />
        </el-form-item>
        <el-form-item label="共享设置">
          <el-radio-group v-model="shareUserId">
            <el-radio-button :label="0">仅自己可见</el-radio-button>
            <el-radio-button :label="1">共享给指定用户</el-radio-button>
            <el-radio-button :label="2">共享给所有用户</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="shareUserId === 1" label="请选择用户">
          <el-select
            v-model="shareUserIdArr"
            placeholder="请选择用户"
            style="width: 360px"
            multiple
            clearable
          >
            <el-option
              v-for="item in shareUserList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-end">
          <el-button @click="isShowSaveUserFilterPlan = false">取消</el-button>
          <el-button type="primary" @click="saveUserFilterPlan">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { nextTick, ref, onMounted, watch, computed } from "vue";
import { Search, Tools, Refresh, Delete, Close } from "@element-plus/icons-vue";
import QualityInput from "./qualityInput.vue";
import dayjs from "dayjs";
import filterInput from "./filterInput.vue";
import customFilterInput from "./customFilterInput.vue";
import { debounce } from "lodash";
import { useI18n } from "vue-i18n";
import ButtonGroup from "@/components/ReTable/ButtonGroup.vue";
import { addScheme, delScheme, queryScheme } from "@/api/system/common";
import { useUserStoreHook } from "@/store/modules/user";
import { useRouter } from "vue-router";
import { getUserList } from "@/api/system/user";
import { ElMessage, ElMessageBox } from "element-plus";
const { t } = useI18n(); // 解构出t方法

const props = defineProps({
  columns: {
    type: Array,
    required: true
  },
  topBtns: {
    type: Array,
    default: () => []
  },
  fastFilter: {
    type: Object,
    default: () => {}
  },
  /** 根据定义的枚举值进行字段搜索类型区分  * @type {Array} */
  mappingStatus: {
    type: Array,
    default: () => []
  },
  /** 查询数据，用于初始化筛选框  * @type {Object} */
  queryData: {
    type: Object,
    default: () => ({})
  }
});

const emits = defineEmits(["update-filter", "reset-filter"]);

// 用戶自定义筛选方案
const customFiltersPlan = ref([
  {
    checked: true,
    schemeName: "默认方案",
    schemeContent: [
      {
        index: 0,
        name: "",
        condition: "",
        query: "",
        logic: ""
      },
      {
        index: 1,
        name: "",
        condition: "",
        query: "",
        logic: ""
      }
    ]
  }
]);

const currentFilterPlan = computed(() => {
  return customFiltersPlan.value.find(item => item.checked);
});

const router = useRouter();

const menuId = router.currentRoute.value.path.replaceAll("/", "");

// 获取当前用户筛选方案
const requestUserFilterPlan = async () => {
  customFiltersPlan.value = [
    {
      checked: true,
      schemeName: "默认方案",
      schemeContent: [
        {
          index: 0,
          name: "",
          condition: "",
          query: "",
          logic: ""
        },
        {
          index: 1,
          name: "",
          condition: "",
          query: "",
          logic: ""
        }
      ]
    }
  ];
  const params = {
    menuId: menuId
  };
  const res = await queryScheme(params);
  if (res.data) {
    res.data.forEach(item => {
      item.schemeContent = JSON.parse(item.schemeContent);
    });
    customFiltersPlan.value.push(...res.data);
  }
};
requestUserFilterPlan();

// 切换筛选方案
const handleFilterPlanChange = activeItem => {
  const hasNotSaved =
    activeItem.schemeName !== "自定义方案-未保存" &&
    customFiltersPlan.value.some(
      item => item.schemeName === "自定义方案-未保存"
    );
  if (hasNotSaved) {
    ElMessageBox.confirm("自定义方案未保存，是否保存方案？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    })
      .then(async () => {
        // 保存方案
        isShowSaveUserFilterPlan.value = true;
      })
      .catch(() => {
        // // 取消保存
        // customFiltersPlan.value = customFiltersPlan.value.filter(
        //   item => item.schemeName !== "自定义方案-未保存"
        // );
      });
  }
  customFiltersPlan.value.forEach(item => {
    item.checked = item.schemeName === activeItem.schemeName;
  });
  const combinedFilters = activeItem.schemeContent.filter(item => item.query);
  emits("update-filter", combinedFilters);
};

const QualityFilterRef = ref(null);

// 获取子组件的筛选数据

const filterInputs = ref([]);
const selVals = ref([]);

// 标记用户是否手动清空或重置了筛选框
const userManuallyReset = ref(false);

// 处理默认筛选框
const processFilters = selVals => {
  const filters = [];
  const filtersPush = ({
    selectedCondition1,
    selectedField1,
    inputValue1,
    logic = "and"
  }) => {
    filters.push({
      name: selectedField1,
      condition: selectedCondition1,
      query: inputValue1,
      logic: logic
    });
  };
  if (selVals.length > 0) {
    selVals.forEach(item => {
      filtersPush(item);
    });
  }
  return filters;
};

// 应用筛选过滤
const applyFilter = debounce(() => {
  nextTick(() => {
    try {
      selVals.value = filterInputs.value.map(item => {
        return item.getChildrenSelVal();
      });

      // 检查是否有用户输入的筛选条件
      const hasUserInput = selVals.value.some(
        item => item.selectedField1 && item.inputValue1
      );

      // 如果用户输入了筛选条件，设置标志
      if (hasUserInput) {
        userManuallyReset.value = true;
        console.log("用户手动输入了筛选条件，设置标志防止自动填充");
      }
    } catch (error) {
      console.error("Error while calling clearChildrenSelVal", error);
    }

    const filters = processFilters(selVals.value);

    if (fastSelVal.value) {
      filters.push({
        disabled: true,
        name: props.fastFilter.name,
        condition: "eq",
        query: fastSelVal.value,
        logic: "and"
      });
    }
    const combinedFilters = filters.filter(item => item.query);
    emits("update-filter", combinedFilters);
  });
}, 300);

// 重置过滤
const resetFilter = debounce(() => {
  nextTick(() => {
    // 设置标志，表示用户手动重置了筛选框
    userManuallyReset.value = true;
    console.log("用户手动重置了筛选框");
    filterInputs.value.forEach(item => {
      item.clearChildrenSelVal();
    });
    QualityFilterRef.value.clearAll();
    emits("reset-filter");
  });
}, 300);

// 快速筛选
const fastSelVal = ref("");

if (props.fastFilter?.default) {
  fastSelVal.value = props.fastFilter.default;
  applyFilter();
}

// 初始化筛选框，如果queryData中存在查询条件，则在第二个筛选框中默认填充
const initFilterFromQueryData = () => {
  console.log("初始化筛选框，queryData:", props.queryData);

  // 如果用户手动重置了筛选框，则不再自动填充
  if (userManuallyReset.value) {
    console.log("用户已手动重置筛选框，不再自动填充");
    return;
  }

  // 如果存在快捷筛选，则不对第二个筛选框进行自动填充
  if (props.fastFilter && Object.keys(props.fastFilter).length > 0) {
    console.log("存在快捷筛选，不对第二个筛选框进行自动填充");
    return;
  }

  if (
    props.queryData &&
    props.queryData.query &&
    props.queryData.query.length > 0
  ) {
    // 获取第一个查询条件
    const firstQuery = props.queryData.query[0];
    console.log("找到查询条件:", firstQuery);

    // 如果是默认方案，则填充到 默认方案 第二个 筛选框
    if (currentFilterPlan.value.schemeName === "默认方案") {
      if (currentFilterPlan.value.schemeContent[1]) {
        currentFilterPlan.value.schemeContent[1] = {
          index: 1,
          name: firstQuery.name,
          condition: firstQuery.condition,
          query: firstQuery.query,
          logic: "and"
        };
      }
    }
  } else {
    console.log("没有查询条件或queryData格式不正确");
  }
};

// 在组件挂载后初始化筛选框
onMounted(() => {
  console.log("组件已挂载，准备初始化筛选框");
  initFilterFromQueryData();
});

// 监听queryData变化，当queryData变化时重新初始化筛选框
watch(
  () => props.queryData,
  newVal => {
    console.log("queryData变化:", newVal);
    // 只有在用户未手动重置筛选框的情况下，才自动填充
    if (
      !userManuallyReset.value &&
      newVal &&
      newVal.query &&
      newVal.query.length > 0
    ) {
      initFilterFromQueryData();
    }
  },
  { deep: true, immediate: true }
);

// 显示高级筛选模态框
const showQualityFilter = () => {
  // 将默认筛选条件传递给高级筛选

  let filters = [];
  if (currentFilterPlan.value.schemeName === "默认方案") {
    try {
      selVals.value = filterInputs.value.map(item => {
        return item.getChildrenSelVal();
      });
    } catch (error) {
      console.error("Error while calling clearChildrenSelVal", error);
    }
    filters = processFilters(selVals.value);
  } else {
    filters = currentFilterPlan.value.schemeContent;
  }

  QualityFilterRef.value.setDefaultFilters([...filters]);

  QualityFilterRef.value.showModal();
};

const showSaveUserFilterPlan = computed(() => {
  return (
    customFiltersPlan.value.findIndex(
      item => item.schemeName === "自定义方案-未保存"
    ) != -1
  );
});

// 高级筛选传递
const handleQualityFilter = filter => {
  const hasCustomFilter = customFiltersPlan.value.findIndex(
    item => item.schemeName === "自定义方案-未保存"
  );
  if (hasCustomFilter !== -1) {
    customFiltersPlan.value.splice(hasCustomFilter, 1);
  }
  const customFilter = {
    checked: true,
    schemeName: "自定义方案-未保存",
    schemeContent: filter
  };
  customFiltersPlan.value.forEach(item => {
    item.checked = false;
  });
  customFiltersPlan.value.push(customFilter);
  emits(
    "update-filter",
    filter.filter(item => item.query)
  );
};

// 显示保存用户自定义筛选方案弹窗
const isShowSaveUserFilterPlan = ref(false);
const customFilterPlanName = ref("");
const shareUserId = ref(0);
const shareUserList = ref([]);
const shareUserIdArr = ref([]);
const handleAddUserFilterPlan = () => {
  // 获取第一个筛选框的值
  isShowSaveUserFilterPlan.value = true;
};
const requestAllUserList = () => {
  getUserList({
    pageNum: 1,
    pageSize: 9999
  }).then(res => {
    shareUserList.value = res.data.resultList;
  });
};
requestAllUserList();

const saveUserFilterPlan = async () => {
  if (shareUserId.value === 2) {
    shareUserIdArr.value = shareUserList.value.map(item => item.id);
  }
  selVals.value = filterInputs.value.map(item => {
    return item.getChildrenSelVal();
  });
  const params = {
    schemeName: customFilterPlanName.value,
    shareUserId: shareUserIdArr.value,
    schemeContent: JSON.stringify(processFilters(selVals.value)),
    menuId: menuId
  };
  const res = await addScheme(params);
  if (res.code === 200) {
    ElMessage.success("筛选方案保存成功");
    isShowSaveUserFilterPlan.value = false;
    await requestUserFilterPlan();
    customFiltersPlan.value.forEach(item => {
      item.checked = item.schemeName === customFilterPlanName.value;
    });
  }
};

const handleDeleteUserFilterPlan = item => {
  ElMessageBox.confirm("是否确认删除该快捷过滤方案？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(async () => {
      if (item.schemeName === "自定义方案-未保存") {
        customFiltersPlan.value.splice(
          customFiltersPlan.value.findIndex(
            item => item.schemeName === "自定义方案-未保存"
          ),
          1
        );
        customFiltersPlan.value[0].checked = true;
      } else {
        // 保存方案
        const params = {
          id: item.id,
          userId: item.userId,
          menuId: menuId
        };
        delScheme(params).then(res => {
          if (res.code === 200) {
            ElMessage.success("删除成功");
            requestUserFilterPlan();
          }
        });
      }
    })
    .catch(() => {});
};
</script>

<style lang="scss" scoped>
.isFlex-default-none {
  display: none;
}

.isFlex {
  display: block;
}

@media (width <= 1600px) {
  .isFlex {
    display: none;
  }

  .isFlex-default-none {
    display: block;
  }
}

:deep(.el-button + .el-button) {
  margin-left: 0;
}

.custom-tooltip {
  cursor: pointer;
}

.flex {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.filter-component {
  display: flex;
  // flex-flow: column wrap;
  gap: 10px;
  justify-content: space-between;
  margin: 15px 0 2px;
}

.filter-set {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.filter-element {
  width: 200px;
}

.filter-element-type {
  width: 110px;
}

.filter-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: center;
  justify-content: flex-end;
  min-width: 120px;
}

@media (width <= 450px) {
  .flex {
    flex-flow: nowrap;
  }

  .filter-set,
  .filter-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-element,
  .filter-element-type,
  .filter-actions {
    width: 100% !important;
  }
}
</style>
