import { http } from "@/utils/http";

/** 获取物料欠料列表  */
export const getMaterialShortageDataAPI = () => {
  return http.request<any>(
    "post",
    `/api/materia-shortage-situation/three_sales_orders`,
    undefined,
    {
      timeout: 60000 * 10
    }
  );
};

/**
 * 查询所有订单
 */
export const getAllOrderAPI = params => {
  return http.request<any>(
    "get",
    `/api/materia-shortage-situation/getAllOrder`,
    params
  );
};

/**
 * 查询订单欠料情况
 */
export const getOrderShortageAPI = data => {
  return http.request<any>(
    "post",
    `/api/materia-shortage-situation/sales_orders`,
    undefined,
    {
      data: data
    }
  );
};

/**
 * 查询采购明细列表
 */
export const getPurchaseDetailAPI = data => {
  return http.request<any>(
    "post",
    `/api/materia-shortage-situatio/sales_orders/${data.materiaId}`,
    undefined,
    {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded"
      },
      data: data
    }
  );
};

/**
 * 获取订单筛选
 */
export const getOrderScreeningAPI = () => {
  return http.request<any>("get", `/api/order-screening/select`);
};

/**
 * 更新订单筛选
 */
export const updateOrderScreeningAPI = data => {
  return http.request<any>(
    "post",
    `/api/order-screening/updateOrAdd`,
    undefined,
    {
      data: data
    }
  );
};

/**
 * 获取物料筛选
 */
export const getMaterialScreeningAPI = () => {
  return http.request<any>("get", `/api/material-screening/select`);
};

/**
 * 更新物料筛选
 */
export const updateMaterialScreeningAPI = data => {
  return http.request<any>(
    "post",
    `/api/material-screening/updateOrAdd`,
    undefined,
    {
      data: data
    }
  );
};

/**
 * 默认获取物料欠料列表
 */
export const getMatterSelectAPI = data => {
  return http.request<any>("post", `/api/matter/select`, undefined, {
    data: data
  });
};

/**
 * 获取全部物料欠料列表
 */
export const getMatterSelectALLAPI = data => {
  return http.request<any>("post", `/api/matter/select/all`, undefined, {
    data: data
  });
};

/**
 * 获取全部销售订单号列表
 */
export const getMatterSelectSaleOrderAPI = () => {
  return http.request<any>("get", `/api/matter/select/saleOrder`);
};

/**
 * 获取全部物料编码列表
 */
export const getMatterMaterialIdAPI = () => {
  return http.request<any>("get", `/api/matter/select/getMatterMaterialId`);
};

/**
 * 维护订单欠料回料时间和备注
 */
export const updOrAddOrderConfigurationAPI = data => {
  return http.request<any>(
    "post",
    `/api/matter/OrderConfiguration/updOrAdd`,
    undefined,
    {
      data: data
    }
  );
};

/**
 * 更新订单欠料筛选
 */
export const updateOverageScreeningAPI = data => {
  return http.request<any>(
    "post",
    `/api/matter/overageScreening/updateOrAdd`,
    undefined,
    {
      data: data
    }
  );
};

/**
 * @function 获取物料欠料-采购在途列表字段信息
 * @returns {data: any[]} 返回数据
 */
export const getPurTransitDetailFieldAPI = () => {
  return http.request<any>(
    "get",
    `/api/matter/getPurTransitDetail/detail/table/info`
  );
};

/**
 * @function 获取物料欠料-采购在途列表
 * @param data 查询条件
 * @param billNo 订单编号
 * @returns {records: any[]} 返回数据
 */
export const getPurTransitDetailTableDataAPI = (data, materialId) => {
  return http.request<any>(
    "post",
    `/api/matter/getPurTransitDetail/detail/${materialId}`,
    {
      data: data
    }
  );
};

/** 求和采购在途明细数据 */
export const getPurTransitDetailTotalAPI = (
  queryParams,
  name,
  query: any[]
) => {
  return http.request<any>(
    "post",
    `/api/matter/getPurTransitDetail/detail/sum/${queryParams.materialId}/${name}`,
    {
      data: query
    }
  );
};

/**
 * 获取列设置
 */
export const getMatterColumnAPI = () => {
  return http.request<any>("get", `/api/matter-column/select`);
};

/**
 * 更新列设置
 */
export const updateMatterColumnAPI = data => {
  return http.request<any>(
    "post",
    `/api/matter-column/updateOrAdd`,
    undefined,
    {
      data: data
    }
  );
};
