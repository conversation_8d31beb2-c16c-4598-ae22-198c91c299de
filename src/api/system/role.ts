import { http } from "@/utils/http";
/** 角色管理列表 */
export const getRoleList = (params?: object) => {
  return http.request<any>("get", "/api/role", { params });
};

export const getAllRoleList = () => {
  return http.request<any>("get", "/api/list-all-role");
};

// 删除角色
export const delRoleList = id => {
  return http.request<any>("delete", `/api/role/del/${id}`);
};

// 查询当前角色关联用户
export const getUserByRoleId = roleId => {
  return http.request<any>("get", `/api/role/getUserByRoleId/${roleId}`);
};

// 修改角色
export const updateRoleList = data => {
  return http.request<any>("put", `/api/role/revise`, undefined, {
    data: data
  });
};

// 新增角色
export const addRoleList = (data?: object) => {
  return http.request<any>("post", "/api/role/add", undefined, { data });
};

// 查询表头权限
export const headerRoelList = data => {
  return http.request<any>("post", "/api/header-roel/list", undefined, {
    data
  });
};

// 新增表头权限
export const addHeaderRoelList = data => {
  return http.request<any>("post", "/api/header-roel/add", undefined, { data });
};
