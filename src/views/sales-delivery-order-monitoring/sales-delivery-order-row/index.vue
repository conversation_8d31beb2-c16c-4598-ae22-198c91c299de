<template>
  <div>
    <div>
      <el-row class="flex">
        <Card
          v-for="(item, index) in cardList"
          :key="index"
          class="flex-1"
          :title="item.title"
          :data="item.data"
        />
      </el-row>

      <!-- 伸缩分割线 -->
      <ArrowDivider />

      <DetailList :otherField="otherField" :routeTitle="routeTitle" />
      <ReTable
        ref="tableRef"
        :topButtons="topButtons"
        :columns="columns"
        :fetchData="fetchTableData"
        :headerCellKeys="[
          'createdPlannedIssuanceQuantity',
          'uncreatedPlannedIssuanceQuantity',
          'finalMaterialSourceNo',
          'sourceType',
          'createdPickingOrderQuantity',
          'uncreatedPickingOrderQuantity'
        ]"
        :tableRowWarnStatus="tableRowWarnStatus"
        :status="[]"
        height="auto"
        :queryData="queryData"
        :updateQueryDate="updateQueryDate"
        @pageSizeChange="handlePageSizeChange"
        @currentPageChange="handleCurrentPageChange"
        @updateColumns="updateColumns"
        @selectedRows="handleSelectedRows"
        @rowClick="handleRowClick"
        @cellClick="handleCellClick"
        @InputSearchResetFilter="InputSearchResetFilter"
        @InputSearchUpdateFilter="InputSearchUpdateFilter"
      />
    </div>

    <!-- 对话框 -->
    <el-dialog
      v-model="visible"
      title="报告编辑"
      width="50%"
      draggable
      :modal-class="'dialog-fade'"
      :close-on-click-modal="false"
      @close="close"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="图片">
          <div class="flex flex-wrap gap-4">
            <el-upload
              v-model:file-list="formData.fileList"
              :class="isMobile ? 'w-full w-1/2' : 'w-full'"
              :disabled="true"
              action="#"
              list-type="picture-card"
              :auto-upload="false"
              :on-change="handleChange"
            >
              <!-- 自定义触发按钮 -->
              <template #trigger>
                <div class="custom-trigger" @click="handleCustomClick">
                  <el-icon><Plus /></el-icon>
                </div>
              </template>

              <template #file="{ file }">
                <div>
                  <img
                    class="el-upload-list__item-thumbnail"
                    :src="baseUrl + file.imageUrl"
                    alt=""
                  />
                  <span class="el-upload-list__item-actions">
                    <span
                      class="el-upload-list__item-preview"
                      @click="handlePictureCardPreview(file)"
                    >
                      <el-icon><zoom-in /></el-icon>
                    </span>
                    <span
                      v-if="!disabled"
                      class="el-upload-list__item-delete"
                      @click="handleDownload(file)"
                    >
                      <el-icon><Download /></el-icon>
                    </span>
                    <span
                      v-if="!disabled"
                      class="el-upload-list__item-delete"
                      @click="handleRemove(file)"
                    >
                      <el-icon><Delete /></el-icon>
                    </span>
                  </span>
                </div>
              </template>
            </el-upload>

            <el-dialog v-model="dialogVisible" draggable>
              <img :src="dialogImageUrl" alt="Preview Image" />
            </el-dialog>
          </div>
        </el-form-item>
        <el-form-item label="报告内容" prop="reportContent">
          <el-input
            v-model="formData.remarrk"
            type="textarea"
            :rows="4"
            placeholder="请输入报告内容"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="handleConfirm">提交</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, h, reactive, watch, onMounted } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { salesDeliveryOrderDetailsId } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";
import ArrowDivider from "@/components/ReArrowDivider/index.vue";

import {
  getSalesDeliveryOrderDetailsAPI,
  getSalesDeliveryOrderDetailsFieldAPI,
  getNumberOfOverdueInspectionsDetailAPI,
  getHeadThreeDaysTheNumberOfPendingTestsDetailAPI,
  getHeadSevenDaysTheNumberOfPendingTestsDetailAPI,
  getHeaderNumberOfThirdPartyTestsNotCompletedDetailAPI,
  exportSalesDeliveryOrderDetailsAPI,
  updateSalesDeliveryOrderMonitoringAPI
} from "@/api/sales-delivery-order-monitoring/sales-delivery-order-details";
import { useParentColumnTag } from "@/utils/hooks";
import { useCardData } from "@/utils/hooks";
import {
  Delete,
  Download,
  Message,
  Plus,
  ZoomIn
} from "@element-plus/icons-vue";

// 导入获取billNo相关hook
import { useFetchTableData } from "@/utils/hooks";
import Card from "@/views/home/<USER>/Card/saleCard.vue";
import { useRoute } from "vue-router";
import DetailList from "@/components/ReTable/TableDetail.vue";
import { useEditReportHook } from "../hooks";
import { salesDeliveryOrderRowMap } from "@/views/sales-delivery-order-monitoring/utils/types/sales-delivery-order-row";
defineOptions({
  name: "salesDeliveryOrderRow"
});
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);
const route = useRoute();
const { getParentColumnTag } = useParentColumnTag();
const routeTitle = ref();

// 获得对话框相关hook
const {
  visible,
  formData,
  isMobile,
  baseUrl,
  uploadEdit,
  close,
  handleCustomClick,
  handleRemove,
  handleConfirm
} = useEditReportHook(selectRows);

// 搜素内容
const queryData = ref({
  page: { current: 1, size: 30, default: 20 },
  query: [],
  order: [
    {
      name: "issuanceStatus",
      sort: "desc"
    }
  ]
});

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  getTotalHook,
  handleCurrentPageChange,
  handlePageSizeChange,
  handleEdit,
  columns
} = useFetchTableData(
  salesDeliveryOrderDetailsId,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息

getSortedColumnHeaders(getSalesDeliveryOrderDetailsFieldAPI, columns);

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(
  getSalesDeliveryOrderDetailsAPI,
  {
    states: salesDeliveryOrderRowMap
  },
  false,
  {
    queryId: route.query.billNo
  }
);

const tableRowWarnStatus = reactive([
  {
    columnKey: "issuanceStatus",
    operator: "==",
    threshold: "未发",
    className: "row-pending"
  },
  {
    columnKey: "issuanceStatus",
    operator: "==",
    threshold: "未发完",
    className: "row-pending"
  }
]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureUploadThirdPartyInspection",
    type: "primary",
    size: "small",
    // action: async () => {
    //   try {
    //     await ElMessageBox.confirm("是否上传第三方报告？", "提示", {
    //       confirmButtonText: "确定",
    //       cancelButtonText: "取消",
    //       type: "warning"
    //     });
    //     // 确认后的操作
    //     ElMessage.success("操作成功");
    //   } catch {
    //     // 取消操作
    //     console.log("取消操作");
    //   }
    // }
    action: () => {
      handleEdit(
        "UploadThirdPartyInspection",
        updateSalesDeliveryOrderMonitoringAPI,
        "id"
      );
    }
  },
  {
    label: "查看报告",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportSalesDeliveryOrderDetailsAPI, {
        queryId: route.query.billNo
      });
    }
  },
  {
    label: "buttons.pureEdit",
    type: "primary",
    size: "small",
    action: () => {
      uploadEdit();
    }
  },
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportSalesDeliveryOrderDetailsAPI, {
        queryId: route.query.billNo
      });
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  },
  {
    label: "buttons.pureStatus",
    type: "primary",
    size: "small",
    action: () => {
      handleEdit("status", updateSalesDeliveryOrderMonitoringAPI, "id");
    }
  }
]);

// 某行被点击
const handleRowClick = (row, column, cell) => {
  console.log(row, column, cell, "某行被点击");
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
};
onMounted(() => {
  console.log("route.meta.title", route.meta.title);
  routeTitle.value = route.meta.title;
  console.log("routeTitle.value", routeTitle);
});
const otherField = getParentColumnTag("salesDeliveryOrderMonitoring");

// 定义卡片配置
const cardConfig = [
  {
    title: "第三方检验完成率",
    api: getHeaderNumberOfThirdPartyTestsNotCompletedDetailAPI,
    formatter: data => `${data}%`
  },
  {
    title: "3天临期未检验数量",
    api: getHeadThreeDaysTheNumberOfPendingTestsDetailAPI,
    formatter: data => data.count_3_days
  },
  {
    title: "7天临期未检验数量",
    api: getHeadSevenDaysTheNumberOfPendingTestsDetailAPI,
    formatter: data => data.count_7_days
  },
  {
    title: "逾期未检验数量",
    api: getNumberOfOverdueInspectionsDetailAPI
  }
];
const { cardList } = useCardData(cardConfig);
</script>

<style scope>
.custom-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50%;
  height: 100%;
  cursor: pointer;
}
</style>

<style scoped>
/* 移除原有动画样式 */
</style>

<style>
/* 添加全局过渡动画 */
.dialog-fade-enter-active .el-dialog,
.dialog-fade-leave-active .el-dialog {
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
}

.dialog-fade-enter-from .el-dialog,
.dialog-fade-leave-to .el-dialog {
  opacity: 0;
  transform: translateY(20px);
}

/* 优化遮罩动画同步 */
.dialog-fade-enter-active .el-overlay,
.dialog-fade-leave-active .el-overlay {
  transition: opacity 0.3s ease;
}
</style>
