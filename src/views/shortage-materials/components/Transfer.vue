<template>
  <el-dialog
    v-if="visible"
    :model-value="visible"
    :title="title + '筛选'"
    :style="{ width: `${width}px` }"
    :close-on-click-modal="false"
    draggable
    @close="handleClose"
  >
    <div class="transfer-container disable-select">
      <!-- 隐藏区域 -->
      <div class="table-container hidden-columns">
        <!-- 隐藏列表格 -->
        <el-table
          :data="filteredHiddenData"
          row-key="prop"
          height="300"
          :header-cell-style="{
            position: 'sticky',
            top: '0',
            background: '#fff',
            zIndex: 1
          }"
          empty-text="暂无隐藏数据"
          @selection-change="handleHiddenSelectionChange"
        >
          <el-table-column type="selection" width="40" />
          <el-table-column prop="name" :label="'隐藏' + title">
            <template #header>
              <div
                style="
                  display: flex;
                  flex-direction: column;
                  align-items: flex-start;
                "
              >
                <span style="margin-right: 10px">{{ "隐藏" + title }}</span>
                <el-input
                  v-model="hiddenFilter"
                  size="small"
                  placeholder="输入关键词过滤"
                  clearable
                  style="width: 120px"
                />
              </div>
            </template>
            <template #default="{ row }">
              {{ row.name }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 操作按钮 -->
      <div :class="isMobile ? 'buttons-container-device' : 'buttons-container'">
        <el-button
          type="primary"
          class="btn-margin"
          size="small"
          @click="moveToDisplay"
          >{{ $t("buttons.pureDisplay") }}</el-button
        >
        <el-button
          type="primary"
          class="btn-margin"
          size="small"
          @click="moveToHidden"
          >{{ $t("buttons.pureHide") }}</el-button
        >
      </div>

      <!-- 显示区域 -->
      <div id="sortable-table" class="table-container show-columns">
        <!-- 显示列表格 -->
        <el-table
          :data="filteredDisplayData"
          row-key="prop"
          height="300"
          :row-class-name="'draggable-row'"
          :header-cell-style="{
            position: 'sticky',
            top: '0',
            background: '#fff',
            zIndex: 1
          }"
          empty-text="暂无显示数据"
          @selection-change="handleVisibleSelectionChange"
        >
          <el-table-column type="selection" width="40" />
          <!-- 拖拽手柄列 -->
          <el-table-column v-if="sortable" width="30">
            <template #default="{}">
              <div class="sortable-handle" style="color: #999; cursor: move">
                ☰
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="name" :label="'显示' + title">
            <template #header>
              <div
                style="
                  display: flex;
                  flex-direction: column;
                  align-items: flex-start;
                "
              >
                <span style="margin-right: 10px">{{ "显示" + title }}</span>
                <el-input
                  v-model="displayFilter"
                  size="small"
                  placeholder="输入关键词过滤"
                  clearable
                  style="width: 120px"
                />
              </div>
            </template>
            <template #default="{ row }">
              {{ row.name }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSave">保存</el-button>
    </template>
  </el-dialog>
</template>

<script lang="tsx" setup>
import { ref, watch, computed, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { deviceDetection } from "@pureadmin/utils";
const isMobile = deviceDetection();
import Sortable from "sortablejs";
import { cloneDeep } from "lodash";

// 定义组件 props
const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  title: {
    type: String,
    required: true
  },
  displayData: {
    type: Array,
    required: true
  },
  hiddenData: {
    type: Array,
    required: true
  },
  sortable: {
    type: Boolean,
    default: false
  },
  width: {
    type: String,
    default: "600"
  }
});

// 定义组件 emits
const emit = defineEmits(["save", "close"]);

// 内部数据
const internalDisplayData = ref([]);
const internalHiddenData = ref([]);

// 监听 props 变化
watch(
  () => props.displayData,
  newVal => {
    internalDisplayData.value = newVal.map(item => {
      return {
        name: item
      };
    });
  },
  { deep: true, immediate: true }
);

watch(
  () => props.hiddenData,
  newVal => {
    internalHiddenData.value = newVal.map(item => {
      return {
        name: item
      };
    });
  },
  { deep: true, immediate: true }
);

const selectedHiddenColumns = ref([]);
const selectedVisibleColumns = ref([]);
const displayFilter = ref(""); // 新增显示列过滤关键词
const hiddenFilter = ref(""); // 新增隐藏列过滤关键词

// 添加计算属性实现过滤
const filteredDisplayData = computed(() => {
  return internalDisplayData.value.filter(item =>
    item.name?.toLowerCase().includes(displayFilter.value.toLowerCase())
  );
});

const filteredHiddenData = computed(() => {
  return internalHiddenData.value.filter(item =>
    item.name?.toLowerCase().includes(hiddenFilter.value.toLowerCase())
  );
});

// 处理隐藏列选择变更
const handleHiddenSelectionChange = selection => {
  selectedHiddenColumns.value = selection;
};

// 将选中的隐藏列移动到可见列
const handleVisibleSelectionChange = selection => {
  selectedVisibleColumns.value = selection;
};

// 处理关闭事件
const handleClose = () => {
  emit("close");
};

// 处理保存事件
const handleSave = () => {
  emit("save", {
    displayData: filteredDisplayData.value.map(item => item.name),
    hiddenData: filteredHiddenData.value.map(item => item.name)
  });
  ElMessage.success("保存成功");
  handleClose();
};

// 移动数据逻辑
const moveToHidden = () => {
  if (selectedVisibleColumns.value.length === 0) {
    ElMessage.warning("请先选择要隐藏的列");
    return;
  }
  internalDisplayData.value = internalDisplayData.value.filter(
    item => !selectedVisibleColumns.value.includes(item)
  );
  internalHiddenData.value = [
    ...internalHiddenData.value,
    ...selectedVisibleColumns.value
  ];
  selectedVisibleColumns.value = [];
};

const moveToDisplay = () => {
  if (selectedHiddenColumns.value.length === 0) {
    ElMessage.warning("请先选择要显示的数据");
    return;
  }
  internalHiddenData.value = internalHiddenData.value.filter(
    item => !selectedHiddenColumns.value.includes(item)
  );
  internalDisplayData.value = [
    ...internalDisplayData.value,
    ...selectedHiddenColumns.value
  ];
  selectedHiddenColumns.value = [];
};

watch(
  () => props.visible,
  newVal => {
    if (newVal) {
      nextTick(() => {
        const el = document.querySelector(
          "#sortable-table .el-table__body-wrapper tbody"
        ) as HTMLElement;
        if (el) {
          Sortable.create(el, {
            // 去掉以下属性表示整列都可拖拽
            handle: ".sortable-handle",
            animation: 300,
            delay: 1,
            ghostClass: "blue-background-class",
            forceFallback: true,
            draggable: "tr",
            onEnd: ({ newIndex, oldIndex }) => {
              if (displayFilter.value) {
                ElMessage.warning("请先清空关键词过滤");
                return;
              }
              const movedItem = filteredDisplayData.value.splice(oldIndex, 1);
              filteredDisplayData.value.splice(newIndex, 0, movedItem[0]);
            }
          });
        }
      });
    }
  }
);
</script>

<style scoped lang="scss">
/* 禁用文本选择 */
.disable-select {
  user-select: none;

  /* Safari */

  /* Firefox */

  /* IE10+ */

  /* Opera */
}

.transfer-container {
  display: flex;
  justify-content: space-between;

  .transfer-column {
    width: 45%;
    height: 300px;
    padding: 10px;
    overflow-y: auto;
    border: 1px solid #ccc;
  }

  .transfer-buttons {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 0 10px;
  }
}

.table-container {
  width: 100%;
  border: 1px solid #eee;
}

.buttons-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
  justify-content: center;
  padding: 0 10px; // 增加内边距，防止溢出
}

.btn-margin {
  margin: 20px 0;
}
</style>
