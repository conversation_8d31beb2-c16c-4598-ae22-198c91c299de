<template>
  <div>
    <div class="flex justify-end mt-8">
      <el-button
        type="primary"
        size="small"
        @click="materialsDialogVisible = true"
      >
        物料编码筛选
      </el-button>
      <el-button
        type="primary"
        size="small"
        @click="salesOrderDialogVisible = true"
      >
        销售订单筛选
      </el-button>
      <el-button
        type="primary"
        size="small"
        @click="settingDialogVisible = true"
      >
        列设置
      </el-button>
    </div>
    <vxe-table
      :key="tableKey"
      class="mt-1"
      :scroll-x="{
        enabled: true,
        gt: 0,
        mode: 'wheel',
        scrollToLeftOnChange: false
      }"
      :scroll-y="{ enabled: true, gt: 0, mode: 'wheel' }"
      show-overflow
      border
      :round="true"
      :height="windowHeight"
      :data="filteredTableData"
      :resizable-config="{ minWidth: 40 }"
      :sort-config="{ trigger: 'cell', showIcon: false }"
      :row-config="{ isCurrent: true, isHover: true }"
      :loading="isRefreshing"
      @resizable-change="handleResizableChange"
      @cell-dblclick="handleCellClick"
    >
      <vxe-column
        v-if="isShowColumn('序号')"
        fixed="left"
        type="seq"
        :width="getColumnWidth('seq', 60)"
        resizable
      />
      <vxe-column
        v-if="isShowColumn('物料编码')"
        fixed="left"
        field="materialId"
        title="物料编码"
        :width="getColumnWidth('materialId', 100)"
        resizable
        sortable
      >
        <template #header>
          <div class="column-header">
            <el-tooltip
              class="box-item"
              effect="dark"
              content="物料编码"
              placement="top"
            >
              <div class="w-full">物料编码</div>
            </el-tooltip>
            <vxe-input
              v-model="filters.materialId"
              style="align-items: center; height: 26px"
              placeholder="搜索"
              clearable
              @click.stop
              @input="handleFilterChange"
            />
          </div>
        </template>
      </vxe-column>
      <vxe-column
        v-if="isShowColumn('物料名称')"
        fixed="left"
        field="materialName"
        title="物料名称"
        :width="getColumnWidth('materialName', 160)"
        resizable
        sortable
      >
        <template #header>
          <div class="column-header">
            <el-tooltip
              class="box-item"
              effect="dark"
              content="物料名称"
              placement="top"
            >
              <div class="w-full">物料名称</div>
            </el-tooltip>
            <vxe-input
              v-model="filters.materialName"
              style="align-items: center; height: 26px"
              placeholder="搜索"
              clearable
              @click.stop
              @input="handleFilterChange"
            />
          </div>
        </template>
      </vxe-column>
      <vxe-column
        v-if="isShowColumn('总未领')"
        fixed="left"
        field="allNotPickedQty"
        title="总未领"
        :width="getColumnWidth('allNotPickedQty', 80)"
        resizable
        sortable
      >
        <template #header>
          <div class="column-header">
            <el-tooltip
              class="box-item"
              effect="dark"
              content="总未领"
              placement="top"
            >
              <div class="w-full">总未领</div>
            </el-tooltip>
            <vxe-input
              v-model="filters.allNotPickedQty"
              style="align-items: center; height: 26px"
              placeholder="搜索"
              clearable
              @click.stop
              @input="handleFilterChange"
            />
          </div>
        </template>
      </vxe-column>
      <vxe-column
        v-if="isShowColumn('总欠料')"
        fixed="left"
        field="allMaterialShortageSituation"
        title="总欠料"
        :width="getColumnWidth('allMaterialShortageSituation', 80)"
        resizable
        sortable
      >
        <template #header>
          <div class="column-header">
            <el-tooltip
              class="box-item"
              effect="dark"
              content="总欠料"
              placement="top"
            >
              <div class="w-full">总欠料</div>
            </el-tooltip>
            <vxe-input
              v-model="filters.allMaterialShortageSituation"
              style="align-items: center; height: 26px"
              placeholder="搜索"
              clearable
              @click.stop
              @input="handleFilterChange"
            />
          </div>
        </template>
      </vxe-column>
      <vxe-column
        v-if="isShowColumn('生产不对单未领')"
        fixed="left"
        field="prodNotReceived"
        title="生产不对单未领"
        :width="getColumnWidth('prodNotReceived', 80)"
        resizable
        sortable
      >
        <template #header>
          <div class="column-header">
            <el-tooltip
              class="box-item"
              effect="dark"
              content="生产不对单未领"
              placement="top"
            >
              <div class="w-full">生产不对单未领</div>
            </el-tooltip>
            <vxe-input
              v-model="filters.prodNotReceived"
              style="align-items: center; height: 26px"
              placeholder="搜索"
              clearable
              @click.stop
              @input="handleFilterChange"
            />
          </div>
        </template>
      </vxe-column>
      <vxe-column
        v-if="isShowColumn('委外不对单未领')"
        fixed="left"
        field="subNotReceived"
        title="委外不对单未领"
        :width="getColumnWidth('subNotReceived', 80)"
        resizable
        sortable
      >
        <template #header>
          <div class="column-header">
            <el-tooltip
              class="box-item"
              effect="dark"
              content="委外不对单未领"
              placement="top"
            >
              <div class="w-full">委外不对单未领</div>
            </el-tooltip>
            <vxe-input
              v-model="filters.subNotReceived"
              style="align-items: center; height: 26px"
              placeholder="搜索"
              clearable
              @click.stop
              @input="handleFilterChange"
            />
          </div>
        </template>
      </vxe-column>
      <vxe-column
        v-if="isShowColumn('即时库存')"
        fixed="left"
        field="justInventory"
        title="即时库存"
        :width="getColumnWidth('justInventory', 80)"
        resizable
        sortable
      >
        <template #header>
          <div class="column-header">
            <el-tooltip
              class="box-item"
              effect="dark"
              content="即时库存"
              placement="top"
            >
              <div class="w-full">即时库存</div>
            </el-tooltip>
            <vxe-input
              v-model="filters.justInventory"
              style="align-items: center; height: 26px"
              placeholder="搜索"
              clearable
              @click.stop
              @input="handleFilterChange"
            />
          </div>
        </template>
      </vxe-column>
      <vxe-column
        v-if="isShowColumn('生产在制')"
        fixed="left"
        field="prodMaking"
        title="生产在制"
        :width="getColumnWidth('prodMaking', 80)"
        resizable
        sortable
      >
        <template #header>
          <div class="column-header">
            <el-tooltip
              class="box-item"
              effect="dark"
              content="生产在制"
              placement="top"
            >
              <div class="w-full">生产在制</div>
            </el-tooltip>
            <vxe-input
              v-model="filters.prodMaking"
              style="align-items: center; height: 26px"
              placeholder="搜索"
              clearable
              @click.stop
              @input="handleFilterChange"
            />
          </div>
        </template>
      </vxe-column>
      <vxe-column
        v-if="isShowColumn('采购在途')"
        fixed="left"
        field="purTransit"
        title="采购在途"
        :width="getColumnWidth('purTransit', 80)"
        resizable
        sortable
      >
        <template #header>
          <div class="column-header">
            <el-tooltip
              class="box-item"
              effect="dark"
              content="采购在途"
              placement="top"
            >
              <div class="active-title w-full">采购在途</div>
            </el-tooltip>
            <vxe-input
              v-model="filters.purTransit"
              style="align-items: center; height: 26px"
              placeholder="搜索"
              clearable
              @click.stop
              @input="handleFilterChange"
            />
          </div>
        </template>
      </vxe-column>

      <vxe-colgroup
        v-for="(item, index) in filtersSalesOrder || []"
        :key="item.billNo"
        header-align="center"
      >
        <template #header> 订单交期:{{ item.deliveryDate || "-" }} </template>
        <vxe-colgroup
          header-align="center"
          :title="item.billNo + '/' + item.sytText1"
        >
          <template #header>
            <div class="flex align-center items-center">
              <div class="mr-3">{{ item.billNo + "/" + item.sytText1 }}</div>
              <el-tooltip
                class="box-item"
                effect="dark"
                content="只显示当前订单"
                placement="top"
              >
                <el-button
                  size="small"
                  :icon="item.isShowCurrent ? Check : ''"
                  type="success"
                  circle
                  @click="handleShowCurrentChange(item)"
                />
              </el-tooltip>
            </div>
          </template>
          <vxe-column
            v-if="isShowColumn('生产需求') && item.status !== 2"
            :field="`materialShortageVO[${index}].prodNeedQty`"
            title="生产需求"
            :width="
              getColumnWidth(`materialShortageVO[${index}].prodNeedQty`, 80)
            "
            resizable
            sortable
          >
            <template #header>
              <div class="column-header">
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  content="生产需求"
                  placement="top"
                >
                  <div class="w-full">生产需求</div>
                </el-tooltip>
                <vxe-input
                  v-model="filters[`materialShortageVO[${index}].prodNeedQty`]"
                  style="align-items: center; height: 26px"
                  placeholder="搜索"
                  clearable
                  @click.stop
                  @input="handleFilterChange"
                />
              </div>
            </template>
          </vxe-column>
          <vxe-column
            v-if="isShowColumn('生产未领') && item.status !== 2"
            :field="`materialShortageVO[${index}].prodNotNeck`"
            title="生产未领"
            :width="
              getColumnWidth(`materialShortageVO[${index}].prodNotNeck`, 80)
            "
            resizable
            sortable
          >
            <template #header>
              <div class="column-header">
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  content="生产未领"
                  placement="top"
                >
                  <div class="w-full">生产未领</div>
                </el-tooltip>
                <vxe-input
                  v-model="filters[`materialShortageVO[${index}].prodNotNeck`]"
                  style="align-items: center; height: 26px"
                  placeholder="搜索"
                  clearable
                  @click.stop
                  @input="handleFilterChange"
                />
              </div>
            </template>
          </vxe-column>
          <vxe-column
            v-if="isShowColumn('委外需求') && item.status !== 1"
            :field="`materialShortageVO[${index}].subNeedQty`"
            title="委外需求"
            :width="
              getColumnWidth(`materialShortageVO[${index}].subNeedQty`, 80)
            "
            resizable
            sortable
          >
            <template #header>
              <div class="column-header">
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  content="委外需求"
                  placement="top"
                >
                  <div class="w-full">委外需求</div>
                </el-tooltip>
                <vxe-input
                  v-model="filters[`materialShortageVO[${index}].subNeedQty`]"
                  style="align-items: center; height: 26px"
                  placeholder="搜索"
                  clearable
                  @click.stop
                  @input="handleFilterChange"
                />
              </div>
            </template>
          </vxe-column>
          <vxe-column
            v-if="isShowColumn('委外未领') && item.status !== 1"
            :field="`materialShortageVO[${index}].subNotNeck`"
            title="委外未领"
            :width="
              getColumnWidth(`materialShortageVO[${index}].subNotNeck`, 80)
            "
            resizable
            sortable
          >
            <template #header>
              <div class="column-header">
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  content="委外未领"
                  placement="top"
                >
                  <div class="w-full">委外未领</div>
                </el-tooltip>
                <vxe-input
                  v-model="filters[`materialShortageVO[${index}].subNotNeck`]"
                  style="align-items: center; height: 26px"
                  placeholder="搜索"
                  clearable
                  @click.stop
                  @input="handleFilterChange"
                />
              </div>
            </template>
          </vxe-column>
          <vxe-column
            v-if="isShowColumn('欠料')"
            :field="`materialShortageVO[${index}].orderMaterialShortageSituationQty`"
            title="欠料"
            :width="
              getColumnWidth(
                `materialShortageVO[${index}].orderMaterialShortageSituationQty`,
                120
              )
            "
            resizable
            sortable
          >
            <template #header>
              <div class="column-header">
                <div>
                  <el-select
                    v-model="item.status"
                    placeholder="Select"
                    size="small"
                    style="width: 100px"
                    @click.stop
                    @change="handleMaterialsTypeChange(item)"
                  >
                    <el-tooltip
                      v-for="item in materialsTypeOptions"
                      :key="item.value"
                      class="box-item"
                      effect="dark"
                      :content="item.label"
                      placement="top"
                    >
                      <el-option
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-tooltip>
                  </el-select>
                </div>
                <vxe-input
                  v-model="
                    filters[
                      `materialShortageVO[${index}].orderMaterialShortageSituationQty`
                    ]
                  "
                  style="align-items: center; height: 26px"
                  placeholder="搜索"
                  clearable
                  @click.stop
                  @input="handleFilterChange"
                />
              </div>
            </template>
          </vxe-column>
          <vxe-column
            v-if="isShowColumn('欠料回料时间')"
            :field="`materialShortageVO[${index}].backTime`"
            title="欠料回料时间"
            :width="
              getColumnWidth(`materialShortageVO[${index}].backTime`, 120)
            "
            resizable
            sortable
          >
            <template #header>
              <div class="column-header">
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  content="欠料回料时间"
                  placement="top"
                >
                  <div class="w-full">欠料回料时间</div>
                </el-tooltip>
                <vxe-date-picker
                  v-model="filters[`materialShortageVO[${index}].backTime`]"
                  type="date"
                  placeholder="选择日期时间"
                  clearable
                  style="align-items: center; height: 26px"
                  @click.stop
                  @change="handleFilterChange"
                />
              </div>
            </template>
            <template #default="{ row }">
              <el-date-picker
                v-if="!row.materialShortageVO[index].backTime"
                v-model="row.materialShortageVO[index].backTimeInputValue"
                type="datetime"
                placeholder="选择日期时间"
                size="small"
                clearable
                style="align-items: center; width: 100%; height: 26px"
                @change="
                  handleSaveOrderConfiguration(row.materialShortageVO[index], 1)
                "
              />
              <span v-else>{{
                dayjs(row.materialShortageVO[index].backTime).format(
                  "YYYY-MM-DD HH:mm:ss"
                )
              }}</span>
            </template>
          </vxe-column>
          <vxe-column
            v-if="isShowColumn('备注')"
            :field="`materialShortageVO[${index}].remarks`"
            title="备注"
            :width="getColumnWidth('remarks', 160)"
            resizable
            sortable
          >
            <template #header>
              <div class="column-header">
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  content="备注"
                  placement="top"
                >
                  <div class="w-full">备注</div>
                </el-tooltip>
                <vxe-input
                  v-model="filters[`materialShortageVO[${index}].remarks`]"
                  style="align-items: center; height: 26px"
                  placeholder="搜索"
                  clearable
                  @click.stop
                  @input="handleFilterChange"
                />
              </div>
            </template>
            <template #default="{ row }">
              <el-input
                v-if="!row.materialShortageVO[index].remarks"
                v-model="row.materialShortageVO[index].remarksInputValue"
                :autosize="{ minRows: 2, maxRows: 4 }"
                placeholder="请输入"
                @keydown.enter="
                  handleSaveOrderConfiguration(row.materialShortageVO[index], 2)
                "
              >
                <template #append>
                  <el-button
                    style="flex-shrink: 0"
                    type="primary"
                    size="small"
                    @click="
                      handleSaveOrderConfiguration(
                        row.materialShortageVO[index],
                        2
                      )
                    "
                  >
                    保存
                  </el-button>
                </template>
              </el-input>
              <span v-else>{{ row.materialShortageVO[index].remarks }}</span>
            </template>
          </vxe-column>
          <!--          <vxe-column field="leftovers" title="剩余料" width="80" />-->
        </vxe-colgroup>
      </vxe-colgroup>
    </vxe-table>

    <!--  物料编码配置穿梭框  -->
    <Transfer
      :visible="materialsDialogVisible"
      title="物料编码"
      :displayData="materialsDialogVisibleData"
      :hiddenData="materialsDialogHiddenData"
      @close="closeTransfer"
      @save="saveMaterialsTransfer"
    />

    <!--  销售订单配置穿梭框  -->
    <Transfer
      :visible="salesOrderDialogVisible"
      title="销售订单"
      width="800"
      :sortable="true"
      :displayData="salesOrderTransferVisibleData"
      :hiddenData="salesOrderTransferHiddenData"
      @close="closeTransfer"
      @save="saveSalesOrderTransfer"
    />

    <!--  列设置配置穿梭框  -->
    <Transfer
      :visible="settingDialogVisible"
      title="列"
      :displayData="columnTransferVisibleData"
      :hiddenData="columnTransferHiddenData"
      @close="closeTransfer"
      @save="saveColumnTransfer"
    />
  </div>
</template>

<script lang="tsx" setup>
import { ref, computed, reactive, onMounted, nextTick } from "vue";
import {
  getMaterialScreeningAPI,
  getMatterColumnAPI,
  getMatterMaterialIdAPI,
  getMatterSelectALLAPI,
  getMatterSelectAPI,
  getMatterSelectSaleOrderAPI,
  getOrderScreeningAPI,
  updateMaterialScreeningAPI,
  updateMatterColumnAPI,
  updateOrderScreeningAPI,
  updateOverageScreeningAPI,
  updOrAddOrderConfigurationAPI
} from "@/api/shortage-materials";
import { debounce } from "lodash-es";
import { cloneDeep } from "@pureadmin/utils";
import router from "@/router";
import { ElInput, ElMessage } from "element-plus";
import { Check } from "@element-plus/icons-vue";
import dayjs from "dayjs";
import Transfer from "./components/Transfer.vue";

const tableData = ref([]);
const filteredTableData = ref([]);
const allfilteredTableData = ref([]);
const materialsDialogVisible = ref(false);
const materialsDialogVisibleData = ref([]);
const materialsDialogHiddenData = ref([]);
const materialsDialogData = ref([]);
const salesOrderDialogVisible = ref(false);
const salesOrderDialogVisibleData = ref([]);
const salesOrderDialogHiddenData = ref([]);

const salesOrderTransferVisibleData = ref([]);
const salesOrderTransferHiddenData = ref([]);

const settingDialogVisible = ref(false);
const columnTransferVisibleData = ref([]);
const columnTransferHiddenData = ref([]);

const salesOrderDialogData = ref([]);
const filtersSalesOrder = ref([]);
const isRefreshing = ref(false);
const tableKey = ref(0); // 用于强制重新渲染表格
const materialsTypeOptions = ref([
  {
    label: "全部欠料",
    value: 0
  },
  {
    label: "生产欠料",
    value: 1
  },
  {
    label: "委外欠料",
    value: 2
  }
]);

// 欠料类型更改
const handleMaterialsTypeChange = async item => {
  const data = {
    saleOrder: item.billNo,
    status: item.status
  };

  try {
    const res = await updateOverageScreeningAPI(data);
    if (res.code === 200) {
      // 使用当前的筛选条件刷新数据
      await requestMatterSelectData(
        materialsDialogVisibleData.value,
        salesOrderDialogVisibleData.value
      );
    }
  } catch (error) {
    console.error("更新物料类型筛选失败:", error);
  }
};

// 请求物料欠料一览数据
const requestMatterSelectData = async (materialIdList = [], orderList = []) => {
  try {
    isRefreshing.value = true;
    console.log("开始刷新物料欠料数据...", { materialIdList, orderList });

    const params = {
      materialIdList,
      orderList
    };
    const res = await getMatterSelectALLAPI(params);

    if (res.data) {
      // 清空旧数据，确保重新渲染
      tableData.value = [];
      filteredTableData.value = [];

      // 使用 nextTick 确保 DOM 更新后再设置新数据
      await nextTick();

      // 设置新数据
      tableData.value = res.data;
      filteredTableData.value = res.data;

      console.log("物料欠料数据更新完成，记录数:", res.data.length);

      // 重新组装订单数据
      await requestMatterSelectSaleOrder();
    }
  } catch (error) {
    console.error("获取物料欠料数据失败:", error);
  } finally {
    isRefreshing.value = false;
  }
};
requestMatterSelectData();

// 请求所有销售订单列表
const allOrder = ref([]);
const requestMatterSelectSaleOrder = async () => {
  try {
    console.log("开始重新组装订单数据...");
    const res = await getMatterSelectSaleOrderAPI();

    if (res.data) {
      allOrder.value = res.data.records.map(item => {
        return {
          ...item,
          isShowCurrent: false
        };
      });
      await requestOrderScreeningAPIData();

      // 拼接表格数据和订单数据
      tableData.value.forEach(tableItem => {
        let reMaterialShortageVO = allOrder.value.map(order => {
          return {
            saleOrderNo: order.billNo,
            materialId: tableItem.materialId,
            prodNeedQty: 0,
            prodNotNeck: 0,
            subNeedQty: 0,
            subNotNeck: 0,
            orderMaterialShortageSituationQty: 0,
            backTime: null,
            remarks: "",
            sytText1: order.sytText1,
            deliveryDate: order.deliveryDate,
            deliveryRemainingDay: order.deliveryRemainingDay
          };
        });

        // 如果存在原有的订单数据，则合并
        if (
          tableItem.materialShortageVO &&
          tableItem.materialShortageVO.length > 0
        ) {
          const deepClone = cloneDeep(tableItem.materialShortageVO);
          deepClone.forEach(item => {
            reMaterialShortageVO.forEach(orderItem => {
              if (item.saleOrderNo === orderItem.saleOrderNo) {
                orderItem.prodNeedQty = item.prodNeedQty;
                orderItem.prodNotNeck = item.prodNotNeck;
                orderItem.subNeedQty = item.subNeedQty;
                orderItem.subNotNeck = item.subNotNeck;
                orderItem.orderMaterialShortageSituationQty =
                  item.orderMaterialShortageSituationQty;
                orderItem.backTime = item.backTime;
                orderItem.remarks = item.remarks;
              }
            });
          });
        }

        const billNos = filtersSalesOrder.value.map(item => {
          return item.billNo;
        });

        tableItem.materialShortageVO = reMaterialShortageVO
          .filter(item => {
            return billNos.includes(item.saleOrderNo);
          })
          .sort((a, b) => {
            const indexA = billNos.indexOf(a.saleOrderNo);
            const indexB = billNos.indexOf(b.saleOrderNo);
            return indexA - indexB;
          });
      });

      // 强制更新过滤后的数据
      filteredTableData.value = [...tableData.value];

      allfilteredTableData.value = cloneDeep(filteredTableData.value);

      // 强制重新渲染表格
      tableKey.value += 1;

      console.log("订单数据重新组装完成");
    }
  } catch (error) {
    console.error("重新组装订单数据失败:", error);
  }
};

// 请求所有物料列表
const allMaterial = ref([]);
const requestMatterSelectMaterial = async () => {
  const res = await getMatterMaterialIdAPI();
  if (res.data) {
    allMaterial.value = res.data.records;
    await requestMatterSelectSaleOrderTransferData();
  }
};
requestMatterSelectMaterial();

// 列宽存储
const COLUMN_WIDTHS_STORAGE_KEY = "shortage-materials-column-widths";
const columnWidths = ref({});

// 获取列宽，优先使用保存的宽度，其次使用默认宽度
const getColumnWidth = (field, defaultWidth) => {
  return columnWidths.value[field] || defaultWidth;
};

// 从本地存储加载列宽设置
onMounted(() => {
  try {
    const savedWidths = localStorage.getItem(COLUMN_WIDTHS_STORAGE_KEY);
    if (savedWidths) {
      columnWidths.value = JSON.parse(savedWidths);
    }
  } catch (error) {
    console.error("加载列宽设置失败:", error);
  }
});

// 处理列宽变化
const handleResizableChange = ({ column, resizeWidth }) => {
  if (column.field) {
    // 使用 resizeWidth 参数，这是调整后的新宽度
    columnWidths.value[column.field] = resizeWidth;
    try {
      localStorage.setItem(
        COLUMN_WIDTHS_STORAGE_KEY,
        JSON.stringify(columnWidths.value)
      );
    } catch (error) {
      console.error("保存列宽设置失败:", error);
    }
  }
};

// 计算窗口高度
const windowHeight = computed(() => {
  return (
    (window.innerHeight ||
      document.documentElement.clientHeight ||
      document.body.clientHeight) -
    168 +
    "px"
  );
});

// 处理过滤条件变化
const filters = reactive({}); // 定义过滤条件对象
const handleFilterChange = debounce(() => {
  // 先恢复原始数据
  let filtered = [...tableData.value];

  // 遍历所有过滤条件
  for (const [key, value] of Object.entries(filters)) {
    // 如果过滤值为空，则跳过该条件
    if (!value || value === "") continue;

    // 处理嵌套字段，如 materialShortageVO[0].prodNeedQty
    if (key.includes("materialShortageVO")) {
      const matches = key.match(/materialShortageVO\[(\d+)\]\.(\w+)/);
      if (matches && matches.length === 3) {
        const index = parseInt(matches[1]);
        const field = matches[2];

        filtered = filtered.filter(item => {
          if (
            !item.materialShortageVO ||
            !item.materialShortageVO[index] ||
            item.materialShortageVO[index][field] === undefined
          ) {
            return false;
          }

          const itemValue = String(
            item.materialShortageVO[index][field]
          ).toLowerCase();
          return itemValue.includes(String(value).toLowerCase());
        });
      }
    } else {
      // 处理普通字段
      filtered = filtered.filter(item => {
        if (item[key] === undefined) return false;
        const itemValue = String(item[key]).toLowerCase();
        return itemValue.includes(String(value).toLowerCase());
      });
    }
  }

  // 更新过滤后的数据
  filteredTableData.value = filtered;
}, 300);

// 物料编码穿梭框
const requestMatterSelectSaleOrderTransferData = async () => {
  materialsDialogData.value = allMaterial.value.map(item => {
    return {
      key: item.materialId,
      label: item.materialId
    };
  });
  const res = await getMaterialScreeningAPI();
  if (res.data) {
    materialsDialogVisibleData.value = JSON.parse(res.data.materialIdShow);
    materialsDialogHiddenData.value = JSON.parse(res.data.materialIdHide);
  } else {
    const allMaterialClone = cloneDeep(allMaterial.value);
    materialsDialogVisibleData.value = allMaterialClone.map(item => {
      return item.materialId;
    });
  }
};

const requestUpdateMaterialScreeningAPI = async () => {
  const params = {
    materialIdShow: JSON.stringify(materialsDialogVisibleData.value),
    materialIdHide: JSON.stringify(materialsDialogHiddenData.value)
  };
  await updateMaterialScreeningAPI(params);
};

// 确认物料筛选结果
const saveMaterialsTransfer = async data => {
  materialsDialogVisibleData.value = data.displayData;
  materialsDialogHiddenData.value = data.hiddenData;
  await requestUpdateMaterialScreeningAPI();
  // requestMatterSelectData 内部已经包含了订单数据重组和筛选
  await requestMatterSelectData(
    materialsDialogVisibleData.value,
    salesOrderDialogVisibleData.value
  );
};

// 销售订单配置穿梭框
const requestOrderScreeningAPIData = async () => {
  salesOrderDialogData.value = allOrder.value.map(item => {
    return {
      key: item.billNo,
      label: item.billNo,
      sytText1: item.sytText1
    };
  });
  const res = await getOrderScreeningAPI();
  if (res.data) {
    salesOrderDialogVisibleData.value = JSON.parse(res.data.saleOrderShow);
    filtersSalesOrder.value =
      allOrder.value.filter(item => {
        return salesOrderDialogVisibleData.value.includes(item.billNo);
      }) || [];
    filtersSalesOrder.value = [...filtersSalesOrder.value].sort((a, b) => {
      const indexA = salesOrderDialogVisibleData.value.indexOf(a.billNo);
      const indexB = salesOrderDialogVisibleData.value.indexOf(b.billNo);
      return indexA - indexB;
    });
  } else {
    const allOrderClone = cloneDeep(allOrder.value);
    salesOrderDialogVisibleData.value = allOrderClone.map((item, index) => {
      if (index < 3) {
        return item.billNo;
      }
    });
    filtersSalesOrder.value = allOrderClone.slice(0, 3);
  }
  salesOrderDialogHiddenData.value = salesOrderDialogData.value
    .map(item => item.key)
    .filter(item => {
      return !salesOrderDialogVisibleData.value.includes(item);
    });
  salesOrderTransferHiddenData.value = salesOrderDialogData.value
    .filter(item => {
      return !salesOrderDialogVisibleData.value.includes(item.key);
    })
    .map(item => item.key + "/" + item.sytText1);
  salesOrderTransferVisibleData.value = salesOrderDialogData.value
    .filter(item => {
      return salesOrderDialogVisibleData.value.includes(item.key);
    })
    .sort((a, b) => {
      const indexA = salesOrderDialogVisibleData.value.indexOf(a.key);
      const indexB = salesOrderDialogVisibleData.value.indexOf(b.key);
      return indexA - indexB;
    })
    .map(item => item.key + "/" + item.sytText1);
};

const requestUpdateOrderScreeningAPI = async () => {
  const params = {
    saleOrderShow: JSON.stringify(salesOrderDialogVisibleData.value),
    saleOrderHide: JSON.stringify(salesOrderDialogHiddenData.value)
  };
  await updateOrderScreeningAPI(params);
};

// 确认销售订单筛选结果
const saveSalesOrderTransfer = async data => {
  salesOrderTransferVisibleData.value = data.displayData;
  salesOrderTransferHiddenData.value = data.hiddenData;

  salesOrderDialogVisibleData.value = salesOrderTransferVisibleData.value.map(
    item => {
      return item.split("/")[0];
    }
  );
  salesOrderDialogHiddenData.value = salesOrderTransferHiddenData.value.map(
    item => {
      return item.split("/")[0];
    }
  );
  await requestUpdateOrderScreeningAPI();
  // requestMatterSelectData 内部已经包含了订单数据重组和筛选
  await requestMatterSelectData(
    materialsDialogVisibleData.value,
    salesOrderDialogVisibleData.value
  );
};

// 处理单元格点击
const handleCellClick = (row, column, cell) => {
  // 采购在途
  if (row.column.field === "purTransit") {
    // 跳转明细页携带materiaId
    const materialId = row.row.materialId;
    router.push({
      path: "/shortage-materials/detail",
      query: {
        materialId: materialId
      }
    });
  }
};

// 处理 欠料时间和备注 维护
const handleSaveOrderConfiguration = async (data, type) => {
  console.log("保存订单配置:", data, type);
  let params = {
    saleOrder: data.saleOrderNo,
    materialId: data.materialId,
    backTime: "",
    remarks: ""
  };
  if (type === 1) {
    // 欠料时间
    params.backTime = data.backTimeInputValue;
  } else if (type === 2) {
    // 备注
    params.remarks = data.remarksInputValue;
  }

  try {
    const res = await updOrAddOrderConfigurationAPI(params);
    if (res.code === 200) {
      console.log("保存成功，开始刷新表格数据...");
      // 刷新表格数据并重新渲染
      // requestMatterSelectData 内部已经包含了加载状态管理
      await requestMatterSelectData(
        materialsDialogVisibleData.value,
        salesOrderDialogVisibleData.value
      );
      console.log("表格数据刷新完成");
    }
  } catch (error) {
    console.error("保存配置失败:", error);
  }
};

// 列设置
const defaultColumnSetting = [
  "序号",
  "物料编码",
  "物料名称",
  "总未领",
  "总欠料",
  "生产不对单未领",
  "委外不对单未领",
  "即时库存",
  "生产在制",
  "采购在途",
  "生产需求",
  "生产未领",
  "委外需求",
  "委外未领",
  "欠料",
  "欠料回料时间",
  "备注"
];
const requestColumnSettingAPI = () => {
  getMatterColumnAPI().then(res => {
    if (res.data?.columnShow) {
      columnTransferVisibleData.value = JSON.parse(res.data.columnShow).sort(
        (a, b) => {
          const indexA = defaultColumnSetting.indexOf(a);
          const indexB = defaultColumnSetting.indexOf(b);
          return indexA - indexB;
        }
      );
      columnTransferHiddenData.value = defaultColumnSetting.filter(item => {
        return !columnTransferVisibleData.value.includes(item);
      });
    } else {
      columnTransferVisibleData.value = defaultColumnSetting;
    }
  });
};
requestColumnSettingAPI();
const saveColumnTransfer = data => {
  columnTransferVisibleData.value = data.displayData.sort((a, b) => {
    const indexA = defaultColumnSetting.indexOf(a);
    const indexB = defaultColumnSetting.indexOf(b);
    return indexA - indexB;
  });
  columnTransferHiddenData.value = data.hiddenData;
  const params = {
    columnShow: JSON.stringify(columnTransferVisibleData.value),
    columnHide: JSON.stringify(columnTransferHiddenData.value)
  };
  updateMatterColumnAPI(params);
};
const isShowColumn = columnName => {
  return columnTransferVisibleData.value.includes(columnName);
};

const closeTransfer = () => {
  materialsDialogVisible.value = false;
  salesOrderDialogVisible.value = false;
  settingDialogVisible.value = false;
};

// 显示当前订单数据
const handleShowCurrentChange = currentOrder => {
  filtersSalesOrder.value.forEach(item => {
    if (item.billNo === currentOrder.billNo) {
      item.isShowCurrent = !item.isShowCurrent;
    } else {
      item.isShowCurrent = false;
    }
  });
  if (currentOrder.isShowCurrent) {
    filteredTableData.value = allfilteredTableData.value.filter(item => {
      let isShow = false;
      item.materialShortageVO.forEach(orderItem => {
        if (orderItem.saleOrderNo === currentOrder.billNo) {
          isShow = orderItem.orderMaterialShortageSituationQty > 0;
        }
      });
      return isShow;
    });
  } else {
    filteredTableData.value = allfilteredTableData.value;
  }
};
</script>

<style lang="scss" scoped>
:deep(.vxe-table--header) {
  background-color: #f8f8f9;
}

/* 列头部搜索框样式 */
.column-header {
  display: flex;
  flex-direction: column;
  align-items: center;

  /* 确保列标题文本居中 */
  div {
    overflow: hidden;
    font-weight: bold;
    text-align: center;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /* 调整输入框样式 */
  :deep(.vxe-input) {
    width: 100%;
    min-width: 40px;
  }

  :deep(.vxe-cell) {
    max-height: none !important;
  }
}

:deep(.vxe-header--row:nth-child(3)) {
  height: 80px;
}

/* 增加列头部高度，以容纳搜索框 */
:deep(.vxe-header--column) {
  min-height: 90px;
  padding: 0 !important;
}

/* 确保表格头部有足够的高度 */
:deep(.vxe-table--header-wrapper) {
  min-height: 90px;
}

.active-title {
  font-weight: bold;
  color: #409efe;
  text-decoration: underline;
}

.active-title:hover {
  color: #337ecc;
}

.column-header > div {
  font-weight: normal;
}
</style>
