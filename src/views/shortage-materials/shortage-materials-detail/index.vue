<template>
  <div>
    <DetailList :otherField="otherField" :routeTitle="routeTitle" />
    <ReTable
      ref="tableRef"
      :mappingStatus="null"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      showTotal
      :getTotal="getTotal"
      :headerCellKeys="[]"
      :tableRowWarnStatus="tableRowWarnStatus"
      :status="[]"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script setup lang="tsx">
import { useRoute } from "vue-router";
import ReTable from "@/components/ReTable/Table.vue";
import { shortageMaterialsDetails } from "@/router/columnIdList";
import { useFetchTableData, useParentColumnTag } from "@/utils/hooks";
import { onMounted, reactive, ref } from "vue";
import {
  getPurTransitDetailFieldAPI,
  getPurTransitDetailTableDataAPI,
  getPurTransitDetailTotalAPI
} from "@/api/shortage-materials";
import DetailList from "@/components/ReTable/TableDetail.vue";
import { shortageMaterialsDetailMap } from "@/views/full-lifecycle/utils/types/ShortageMaterialsDetails";

defineOptions({
  name: "ShortageMaterialsDetails"
});

const routeTitle = ref("");

const route = useRoute();
const { materialId } = route.query;

const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);

// 搜素内容
const queryData = ref({
  page: { current: 1, size: 20, default: 20 },
  query: [],
  order: []
});

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  getTotalHook,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(
  shortageMaterialsDetails,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息
getSortedColumnHeaders(getPurTransitDetailFieldAPI, columns);

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(
  getPurTransitDetailTableDataAPI,
  {
    states: shortageMaterialsDetailMap
  },
  false,
  materialId
);

const tableRowWarnStatus = reactive([]);

// 定义按钮配置
// 定义按钮配置
const topButtons = ref([
  // {
  //   label: "buttons.pureExcelExport",
  //   type: "primary",
  //   size: "small",
  //   action: () => {
  //     handleExportExcel(exportMaterialListDetailsAPI, {
  //       billNo: route.query.billNo,
  //
  //       prodOrderNo: route.query.prodOrderNo
  //     });
  //   }
  // },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);

/**
 * @function 获取求和函数
 * @param {Array} sumList 需要求和字段数组
 * @param {Function} getTotalAPI 获取求和的API
 */
// 需要求和字段数组
const sumList = ref([
  "priceUnitQty",
  "priceBaseQty",
  "entryTaxAmount",
  "taxAmountLc",
  "allAmountLc",
  "entryAmount",
  "amountLc",
  "baseApJoinQty",
  "stockQty",
  "stockBaseQty",
  "receiveQty",
  "remainReceiveQty",
  "stockInQty",
  "stockBaseStockInQty",
  "remainStockInQty",
  "mrbQty",
  "checkRetQty",
  "joinQty",
  "stockRetQty",
  "invoiceQty",
  "invoiceAmount",
  "salQty",
  "qty",
  "baseUnitQty"
]);
const getTotal = getTotalHook(sumList, getPurTransitDetailTotalAPI, {
  materialId: materialId
});

// 某行被点击
const handleRowClick = (row, column, cell) => {
  console.log(row, column, cell, "某行被点击");
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
};

onMounted(() => {
  routeTitle.value = route.meta.title;
});

const otherField = [
  {
    label: "物料编码",
    value: materialId,
    prop: "materialId"
  }
];
</script>

<style scoped lang="scss"></style>
