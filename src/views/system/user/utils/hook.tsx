import "./reset.css";
import dayjs from "dayjs";
import roleForm from "../form/role.vue";
import editForm from "../form/index.vue";
import { zxcvbn } from "@zxcvbn-ts/core";
import { handleTree } from "@/utils/tree";
import { message } from "@/utils/message";
import userAvatar from "@/assets/user.jpg";
import { usePublicHooks } from "../../hooks";
import { addDialog } from "@/components/ReDialog";
import type { PaginationProps } from "@pureadmin/table";
import ReCropperPreview from "@/components/ReCropperPreview";
import type { FormItemProps, RoleFormItemProps } from "../utils/types";
import { compressAndUploadImage } from "@/utils/imageConversion";
import {
  getKeyList,
  // isAllEmpty,
  hideTextAtIndex,
  deviceDetection
} from "@pureadmin/utils";
import { getMenuList } from "@/api/system/menu";
import { getUserList } from "@/api/system/user";
// import { getUserList } from "@/api/system";

// import { getRoleIds } from "@/api/system/user.ts";
import { addUserList } from "@/api/system/user.ts";
import { getAllRoleList } from "@/api/system/role.ts";
import { getDeptList } from "@/api/system/dept.ts";
import {
  delUserList,
  updateUser,
  resetUserPassword,
  assigningRoles
} from "@/api/system/user.ts";
import { baseUrlCors } from "@/utils/http";

import { ElForm, ElInput, ElFormItem, ElProgress } from "element-plus";
// ElMessageBox
import {
  type Ref,
  h,
  ref,
  watch,
  computed,
  reactive,
  onMounted,
  toRaw
} from "vue";
// import { createDiffieHellman } from "crypto";
// import { isValidComponentSize } from "element-plus/es/utils/index.mjs";
// import { template } from "lodash";

// import Form from "../../dept/form.vue";

export function useUser(tableRef: Ref, treeRef: Ref) {
  const form = reactive({
    // 左侧部门树的id
    deptId: "",
    username: "",
    phone: "",
    status: ""
  });
  const form2 = reactive({
    // 左侧部门树的id
    deptId: "",
    name: "",
    username: "",
    // phone: "",
    roleId: "",
    status: ""
  });
  // const form3 = reactive({
  //   password: ""
  // })
  const baseURL = import.meta.env.VITE_BASE_URL;
  const formRef = ref();
  const ruleFormRef = ref();
  const dataList = ref([]);
  const loading = ref(true);
  // 上传头像信息
  const avatarInfo = ref();
  // const switchLoadMap = ref({});
  // const { switchStyle } = usePublicHooks();
  const higherDeptOptions = ref();
  const treeData = ref([]);
  const treeLoading = ref(true);
  const selectedNum = ref(0);
  const selectedrow = ref(0);
  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 20,
    currentPage: 1,
    background: true,
    pageSizes: [20, 50, 100, 200],
    pageNum: 1
  });

  // 判断是否已经选择了部门树节点
  const isDeptSelected = ref(false);

  const { tagStyle } = usePublicHooks();
  console.log(baseURL, "baseURL");

  const columns: TableColumnList = [
    {
      label: "勾选列", // 如果需要表格多选，此处label必须设置
      type: "selection",
      fixed: "left",
      reserveSelection: true // 数据刷新后保留选项
    },
    {
      label: "序号",
      prop: "index",
      minWidth: 50,
      cellRenderer: ({ index }) => String(index + 1)
    },
    {
      label: "用户头像",
      prop: "avatar",
      cellRenderer: ({ row }) => (
        <el-image
          fit="cover"
          preview-teleported={true}
          src={
            row.avatar
              ? baseURL + row.avatar + "?time=" + new Date().getTime()
              : userAvatar
          }
          preview-src-list={Array.of(row.avatar || userAvatar)}
          class="w-[24px] h-[24px] rounded-full align-middle"
        />
      ),
      width: 90
    },
    {
      label: "用户账号",
      prop: "username",
      minWidth: 200,
      resizable: true
    },
    {
      label: "用户姓名",
      prop: "name",
      minWidth: 200
    },
    {
      label: "性别",
      prop: "sex",
      minWidth: 90
    },
    {
      label: "部门",
      prop: "dept.name",
      minWidth: 920,
      formatter: ({ dept }) => {
        if (dept == null) {
          // 如果 phone 是 null 或 undefined，返回一个默认值或空字符串
          return "";
        }
        // 如果 phone 不是 null 或 undefined，则调用 hideTextAtIndex 函数
        return dept.map(item => item.name).join(",");
      }
    },
    {
      label: "角色",
      prop: "roleIds",
      minWidth: 200,
      formatter: ({ roleIds }) => {
        if (roleIds == null || !roleOptions.value) {
          return "";
        }
        // 如果 roleIds 不为空，则调用 map 函数将每个元素转换为字符串并拼接成一个字符串
        const hasRoleOptions = roleOptions.value.filter(
          item => roleIds.indexOf(item.id) !== -1
        );
        return hasRoleOptions.map(item => item.name).join(",");
      }
    },
    {
      label: "手机号码",
      prop: "phone",
      minWidth: 200,
      formatter: ({ phone }) => {
        if (phone == null) {
          // 如果 phone 是 null 或 undefined，返回一个默认值或空字符串
          return "";
        }
        // 如果 phone 不是 null 或 undefined，则调用 hideTextAtIndex 函数
        return hideTextAtIndex(phone, { start: 3, end: 6 });
      }
    },
    {
      label: "状态",
      prop: "status",
      minWidth: 100,
      cellRenderer: ({ row, props }) => (
        <el-tag size={props.size} style={tagStyle.value(row.status)}>
          {row.status === true ? "启用" : "禁用"}
        </el-tag>
      )
    },
    {
      label: "创建时间",
      minWidth: 90,
      prop: "created",
      formatter: ({ created }) => dayjs(created).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      label: "操作",
      fixed: "right",
      width: 180,
      slot: "operation"
    },
    {
      label: "用户编号",
      prop: "id",
      width: 90
    }
  ];
  console.log("columns-------", columns);

  const buttonClass = computed(() => {
    return [
      "!h-[20px]",
      "reset-margin",
      "!text-gray-500",
      "dark:!text-white",
      "dark:hover:!text-primary"
    ];
  });
  // 重置的新密码
  const pwdForm = reactive({
    newPwd: "",
    isValid: false
  });
  const pwdProgress = [
    { color: "#e74242", text: "非常弱" },
    { color: "#EFBD47", text: "弱" },
    { color: "#ffa500", text: "一般" },
    { color: "#1bbf1b", text: "强" },
    { color: "#008000", text: "非常强" }
  ];
  // 当前密码强度（0-4）
  const curScore = ref();
  const roleOptions = ref([]);

  // function onChange({ row, index }) {
  //   ElMessageBox.confirm(
  //     `确认要<strong>${row.status === 1 ? "停用" : "启用"
  //     }</strong><strong style='color:var(--el-color-primary)'>${row.username
  //     }</strong>用户吗?`,
  //     "系统提示",
  //     {
  //       confirmButtonText: "确定",
  //       cancelButtonText: "取消",
  //       type: "warning",
  //       dangerouslyUseHTMLString: true,
  //       draggable: true
  //     }
  //   )
  //     .then(() => {
  //       switchLoadMap.value[index] = Object.assign(
  //         {},
  //         switchLoadMap.value[index],
  //         {
  //           loading: true
  //         }
  //       );
  //       setTimeout(() => {
  //         switchLoadMap.value[index] = Object.assign(
  //           {},
  //           switchLoadMap.value[index],
  //           {
  //             loading: false
  //           }
  //         );
  //         message("已成功修改用户状态", {
  //           type: "success"
  //         });
  //       }, 300);
  //     })
  //     .catch(() => {
  //       row.status === 0 ? (row.status = 1) : (row.status = 0);
  //     });
  // }

  function handleUpdate(row) {
    console.log(row);
  }

  async function handleDelete(row) {
    // message(`您删除了用户编号为${row.id}的这条数据`, { type: "success" });
    await delUserList(row.id).then(res => {
      console.log("res删除", res);

      if (res.code === 200) {
        message(`您删除了用户id为${row.id}的这条数据`, { type: "success" });
      } else {
        message("用户删除失败", { type: "error" });
      }
    });
    onSearch();
  }

  // function handleSizeChange(val: number) {
  //   console.log(`${val} items per page`);
  // }

  // function handleCurrentChange(val: number) {
  //   console.log(`current page: ${val}`);
  // }

  function handleSizeChange(val: number) {
    pagination.pageSize = val;
    pagination.currentPage = 1;
    // console.log(`${val} items per page`);
    onSearch();
  }

  function handleCurrentChange(val: number) {
    pagination.currentPage = val;
    onSearch();
    // console.log("val-------------", val);
    // console.log("pagination------------", pagination);
  }

  /** 当CheckBox选择项发生变化时会触发该事件 */
  function handleSelectionChange(val) {
    console.log("val", val);
    selectedrow.value = val;
    selectedNum.value = val.length;
    // 重置表格高度
    tableRef.value.setAdaptive();
  }

  /** 取消选择 */
  function onSelectionCancel() {
    selectedNum.value = 0;
    // 用于多选表格，清空用户的选择
    tableRef.value.getTableRef().clearSelection();
  }

  /** 批量删除 */
  function onbatchDel() {
    // 返回当前选中的行
    const curSelected = tableRef.value.getTableRef().getSelectionRows();
    console.log("curSelected", curSelected);

    // 接下来根据实际业务，通过选中行的某项数据，比如下面的id，调用接口进行批量删除
    message(`已删除用户编号为 ${getKeyList(curSelected, "id")} 的数据`, {
      type: "success"
    });
    tableRef.value.getTableRef().clearSelection();
    onSearch();
  }

  async function onSearch() {
    console.log(pagination, "pa");
    // if (isDeptSelected.value) {
    //   console.log("部门已选择，跳过搜索");
    //   return;
    // }
    loading.value = true;
    const queryParams = {
      ...toRaw(form2),
      pageSize: pagination.pageSize,
      // pageCount: pagination.currentPage,
      pageNum: pagination.currentPage
    };

    // const { data } = await getUserList(toRaw(form));
    console.log("queryParams------------", queryParams);

    const { data } = await getUserList(queryParams);
    console.log("用户管理666888--------------", data);

    dataList.value = data.resultList;
    pagination.total = data.total;
    pagination.pageSize = data.pageSize;
    // pagination.currentPage = data.currentPage;
    pagination.currentPage = data.pageNum;
    console.log("dataList---------", dataList);
    loading.value = false;
  }
  const resetForm = formEl => {
    pagination.currentPage = 1;
    pagination.pageSize = 10;
    // if (!formEl) return;
    formEl.resetFields();
    form2.deptId = "";
    form2.name = "";
    form2.username = "";
    // form2.phone = "";
    form2.roleId = "";
    form2.status = "";

    if (treeRef.value) {
      treeRef.value.onTreeReset();
    }
    onSearch();
  };

  async function onTreeSelect({ id, selected }) {
    form2.deptId = selected ? id : "";
    isDeptSelected.value = selected; // 设置标志位
    console.log(pagination, "pa");

    loading.value = true;
    const queryParams = {
      deptId: form2.deptId,
      pageSize: pagination.pageSize,
      pageNum: pagination.currentPage
    };

    const { data } = await getUserList(queryParams);
    console.log("用户管理归属部门--------------", data);

    dataList.value = data.resultList;
    pagination.total = data.total;
    pagination.pageSize = data.pageSize;
    // pagination.currentPage = data.currentPage;
    pagination.currentPage = data.pageNum;
    console.log("dataList部门---------", dataList);
    loading.value = false;
  }

  function formatHigherDeptOptions(treeList) {
    // 根据返回数据的status字段值判断追加是否禁用disabled字段，返回处理后的树结构，用于上级部门级联选择器的展示（实际开发中也是如此，不可能前端需要的每个字段后端都会返回，这时需要前端自行根据后端返回的某些字段做逻辑处理）
    if (!treeList || !treeList.length) return;
    const newTreeList = [];
    for (let i = 0; i < treeList.length; i++) {
      treeList[i].disabled = treeList[i].status === 0 ? true : false;
      formatHigherDeptOptions(treeList[i].children);
      newTreeList.push(treeList[i]);
    }
    return newTreeList;
  }

  function openDialog(title = "新增", row?: FormItemProps) {
    console.debug("row.dept检查", row?.dept);
    // row.dept = {
    //   id: 0,
    //   name: ""
    // };
    addDialog({
      title: `${title}用户`,
      props: {
        formInline: {
          title,
          higherDeptOptions: formatHigherDeptOptions(higherDeptOptions.value),
          parentId: row?.dept.map(item => item.id) ?? [], //此处需要转换Number类型
          id: row?.id ?? 0,
          nickname: row?.nickname ?? "",
          name: row?.name ?? "",
          username: row?.username ?? "",
          password: row?.password ?? "",
          phone: row?.phone ?? "",
          email: row?.email ?? "",
          sex: row?.sex ?? "",
          status: row?.status ?? true,
          states: row?.status ?? 1,
          remark: row?.remark ?? "",
          dept: row?.dept.map(item => item.id) ?? [],
          roleIds: row?.roleIds ?? [],
          roleOptions: roleOptions.value ?? [],
          isEdit: title === "修改" // 根据标题判断是否为编辑模式
        }
      },
      width: "46%",
      draggable: true,
      fullscreen: deviceDetection(),
      fullscreenIcon: true,
      closeOnClickModal: false,
      contentRenderer: () => h(editForm, { ref: formRef }),
      beforeSure: (done, { options }) => {
        const FormRef = formRef.value.getRef();
        const curData = options.props.formInline as FormItemProps;

        curData.deptId = curData.parentId ? curData.parentId : 0;
        function chores() {
          message(`您${title}了用户账号为${curData.username}的这条数据`, {
            type: "success"
          });
          done(); // 关闭弹框
          onSearch(); // 刷新表格数据
        }
        FormRef.validate(async valid => {
          if (valid) {
            console.log("curData666999", curData);
            // 表单规则校验通过
            if (title === "新增") {
              // 实际开发先调用新增接口，再进行下面操作
              await addUserList(curData).then(res => {
                if (res.code === 200) {
                  chores();
                } else {
                  message("用户新增失败", { type: "error" });
                }
              });
            } else {
              // 实际开发先调用修改接口，再进行下面操作
              await updateUser(curData).then(res => {
                if (res.code === 200) {
                  chores();
                } else {
                  message("用户修改失败", { type: "error" });
                }
              });
            }
          }
        });
      }
    });
  }

  const cropRef = ref();
  /** 上传头像 */
  function handleUpload(row) {
    const img = new Image();
    img.onload = () => {
      showUploadDialog(baseUrlCors + row.avatar);
    };
    img.onerror = () => {
      // 图片加载失败
      showUploadDialog(userAvatar);
    };
    img.src = baseUrlCors + row.avatar;
    // 显示上传对话框的函数
    function showUploadDialog(imgSrc) {
      addDialog({
        title: "裁剪、上传头像",
        width: "46%",
        closeOnClickModal: false,
        fullscreen: deviceDetection(),
        contentRenderer: () =>
          h(ReCropperPreview, {
            ref: cropRef,
            imgSrc: imgSrc, // 根据判断结果使用相应的图片
            onCropper: info => (avatarInfo.value = info),
            style: { height: "63vh" }
          }),

        beforeSure: async done => {
          console.log("裁剪后的图片信息：", avatarInfo.value);
          const response = await compressAndUploadImage(
            avatarInfo.value.blob,
            "/api/user/uploadAvatar",
            0.7,
            row.id
          );
          row.avatar = response.data;
          console.log("row.avatar", row.avatar);
          if (response.code === 200) {
            done(); // 关闭弹框
            onSearch(); // 刷新表格数据
          }
        },
        closeCallBack: () => cropRef.value.hidePopover()
      });
      onSearch();
    }
  }

  watch(pwdForm, ({ newPwd, isValid }) => {
    curScore.value = !isValid ? -1 : zxcvbn(newPwd).score;
    console.log("isValid", isValid);
  });

  /** 重置密码 */
  function handleReset(row) {
    console.log("密码---------", row);

    addDialog({
      title: `重置 ${row.username} 用户的密码`,
      width: "30%",
      draggable: true,
      closeOnClickModal: false,
      fullscreen: deviceDetection(),
      contentRenderer: () => (
        <>
          <ElForm ref={ruleFormRef} model={pwdForm}>
            <ElFormItem
              prop="newPwd"
              rules={[
                {
                  required: true,
                  message: "请输入新密码",
                  trigger: "blur"
                },
                {
                  validator: (rule, value, callback) => {
                    console.log("value", value);

                    // 检查密码长度是否在8到16位之间
                    if (value.length < 8 || value.length > 16) {
                      pwdForm.isValid = false;
                      callback(new Error("密码长度8-16位"));
                    } else {
                      // 检查密码是否只包含允许的字符
                      const allowedCharsRegex = /^[0-9a-zA-Z!@#$%^&*()_+]+$/;
                      if (!allowedCharsRegex.test(value)) {
                        pwdForm.isValid = false;
                        callback(new Error("密码格式错误"));
                      } else {
                        console.log("规则");
                        pwdForm.isValid = true;
                        callback();
                      }
                    }
                  },
                  trigger: "change"
                }
              ]}
            >
              <ElInput
                clearable
                show-password
                type="password"
                v-model={pwdForm.newPwd}
                placeholder="请输入新密码"
              />
            </ElFormItem>
          </ElForm>
          <div class="mt-4 flex">
            {pwdProgress.map(({ color, text }, idx) => (
              <div
                class="w-[19vw]"
                style={{ marginLeft: idx !== 0 ? "4px" : 0 }}
              >
                <ElProgress
                  striped
                  striped-flow
                  duration={curScore.value === idx ? 6 : 0}
                  percentage={curScore.value >= idx ? 100 : 0}
                  color={color}
                  stroke-width={10}
                  show-text={false}
                />
                <p
                  class="text-center"
                  style={{ color: curScore.value === idx ? color : "" }}
                >
                  {text}
                </p>
              </div>
            ))}
          </div>
        </>
      ),
      closeCallBack: () => (pwdForm.newPwd = ""),
      beforeSure: done => {
        ruleFormRef.value.validate(async valid => {
          if (valid) {
            // 表单规则校验通过
            message(`已成功重置 ${row.username} 用户的密码`, {
              type: "success"
            });
            console.log(pwdForm.newPwd);
            // 根据实际业务使用pwdForm.newPwd和row里的某些字段去调用重置用户密码接口即可
            // resetUserPassword

            await resetUserPassword({
              id: row.id,
              password: pwdForm.newPwd
            }).then(res => {
              if (res.code === 200) {
                message("密码重置成功", { type: "success" });
              } else {
                message("重置密码失败", { type: "error" });
              }
            });
            done(); // 关闭弹框
            onSearch(); // 刷新表格数据
          }
        });
      }
    });
  }

  /** 分配角色 */
  async function handleRole(row) {
    // 选中的角色列表
    const roleId = row.id;
    console.log("roleId--------------", row);
    addDialog({
      title: `分配 ${row.name} 用户的角色`,
      props: {
        formInline: {
          username: row?.username ?? "",
          // nickname: row?.username ?? "",
          ids: row.roleIds,
          roleOptions: roleOptions.value ?? [],
          roleId
        }
      },
      width: "400px",
      draggable: true,
      fullscreen: deviceDetection(),
      fullscreenIcon: true,
      closeOnClickModal: false,
      contentRenderer: () => h(roleForm),
      beforeSure: async (done, { options }) => {
        const curData = options.props.formInline as RoleFormItemProps;
        console.log("curIds", curData.ids);
        curData.roleId = curData.ids;
        curData.userId = row.id;
        console.log("curIds----------------", curData);
        // 根据实际业务使用curData.ids和row里的某些字段去调用修改角色接口即可
        await assigningRoles(curData).then(res => {
          if (res.code === 200) {
            message("分配角色成功", { type: "success" });
          } else {
            message("分配角色失败", { type: "error" });
          }
        });
        done(); // 关闭弹框
        onSearch(); // 刷新表格数据
      }
    });
  }

  onMounted(async () => {
    treeLoading.value = true;
    onSearch();
    const { data: data1 } = await getMenuList();
    console.log("菜单管理列表-----------", data1);

    // const { data: data2 } = await getRoleMenu();
    // console.log("获取角色管理-权限-菜单权限", data2);

    // 归属部门
    // const { data } = await getDeptList();
    // console.log("部门111", data);
    const { data } = await getDeptList({ pageSize: 999, pageNum: 1 });
    const { resultList } = data;
    console.log("用户部门--------", resultList);
    higherDeptOptions.value = handleTree(resultList);
    treeData.value = handleTree(resultList);

    treeLoading.value = false;

    // 角色列表
    roleOptions.value = (await getAllRoleList()).data;
    console.log(roleOptions.value, "8888888888888");
  });

  return {
    form2,
    form,
    loading,
    columns,
    dataList,
    treeData,
    treeLoading,
    selectedNum,
    selectedrow,
    pagination,
    buttonClass,
    roleOptions,
    deviceDetection,
    onSearch,
    resetForm,
    onbatchDel,
    openDialog,
    onTreeSelect,
    handleUpdate,
    handleDelete,
    handleUpload,
    handleReset,
    handleRole,
    handleSizeChange,
    onSelectionCancel,
    handleCurrentChange,
    handleSelectionChange
  };
}
