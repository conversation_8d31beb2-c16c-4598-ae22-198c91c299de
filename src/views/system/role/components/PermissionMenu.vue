<script setup lang="ts">
import { useRole } from "@/views/system/role/utils/hook";
import { ref, computed, nextTick, onMounted, watch } from "vue";
import { delay, subBefore, useResizeObserver } from "@pureadmin/utils";

import Close from "@iconify-icons/ep/close";
import Check from "@iconify-icons/ep/check";
import { addHeaderRoelList, headerRoelList } from "@/api/system/role";
import { ElMessage, ElMessageBox } from "element-plus";

const emit = defineEmits(["someEvent", "closeRoleMenu"]);

const props = defineProps({
  type: {
    type: Number,
    default: 0
  },
  propIsShow: {
    type: Boolean,
    default: false
  },
  propRoleObj: {
    type: Object,
    default: () => ({})
  },
  tableRef: {
    type: Object,
    default: () => ({})
  }
});
const roleObj = ref(props.propRoleObj);

const handleClose = () => {
  // handleMenu({});
  emit("closeRoleMenu");
};

const iconClass = computed(() => {
  return [
    "w-[22px]",
    "h-[22px]",
    "flex",
    "justify-center",
    "items-center",
    "outline-none",
    "rounded-[4px]",
    "cursor-pointer",
    "transition-colors",
    "hover:bg-[#0000000f]",
    "dark:hover:bg-[#ffffff1f]",
    "dark:hover:text-[#ffffffd9]"
  ];
});

const treeRef = ref();
const tableRef = ref(props.tableRef);
const contentRef = ref();
const treeHeight = ref();

const {
  isShow,
  curRow,
  treeData,
  filedTreeData,
  treeProps,
  isLinkage,
  isExpandAll,
  isSelectAll,
  treeSearchValue,
  handleMenu,
  handleSave,
  filterMethod,
  transformI18n,
  onQueryChanged,
  deviceDetection
} = useRole(treeRef);

onMounted(() => {
  useResizeObserver(treeRef, async () => {
    await nextTick();
    delay(60).then(() => {
      console.log(
        tableRef.value.getTableDoms().tableWrapper.style.height,
        "height"
      );

      treeHeight.value = parseFloat(
        subBefore(tableRef.value.getTableDoms().tableWrapper.style.height, "px")
      );
    });
  });
  emit("someEvent", treeRef.value);
});

watch(
  () => props.propRoleObj,
  newVal => {
    roleObj.value = newVal;

    if (treeRef.value) {
      emit("someEvent", treeRef.value); // 触发事件并传递 treeRef
    }
  }
);
isShow.value = props.propIsShow;

const filedData = ref([]);
const filedSettingDialogVisible = ref(false);
const hiddenFiled = ref([]);
const currentHeaderId = ref("");
const nodeClick = async (node, checked) => {
  if (node.header) {
    filedData.value = JSON.parse(node.header).data;
    // 查询表头权限
    currentHeaderId.value = node.addressCoding;
    const data = {
      roleId: roleObj.value.id,
      headerId: currentHeaderId.value
    };
    const res = await headerRoelList(data);
    hiddenFiled.value = res.data.map(item => {
      return item.haeaderName;
    });
    if (hiddenFiled.value.length > 0) {
      checkedFiled.value = filedData.value.filter(item => {
        return !hiddenFiled.value.includes(item.field);
      });
    } else {
      checkedFiled.value = filedData.value;
    }
    checkAllFiled.value = checkedFiled.value.length === filedData.value.length;
    filedSettingDialogVisible.value = true;
  } else {
    filedData.value = [];
  }
};

const checkAllFiled = ref(false);
const isIndeterminate = ref(false);
const checkedFiled = ref([]);

const handleCheckAllChange = val => {
  checkedFiled.value = val ? filedData.value : [];
};

const handleCheckedCitiesChange = val => {
  const checkedCount = val.length;
  checkAllFiled.value = checkedCount === filedData.value.length;
  isIndeterminate.value =
    checkedCount > 0 && checkedCount < filedData.value.length;
};

const handleSaveFiled = () => {
  ElMessageBox.confirm("是否确认保存角色字段权限屏蔽修改？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(async () => {
      // 确认
      const currentHiddenFiled = filedData.value.filter(item => {
        return !checkedFiled.value.includes(item);
      });
      const headerName = currentHiddenFiled.map(item => {
        return item.field;
      });
      const data = {
        roleId: roleObj.value.id,
        headerName: headerName,
        tableId: currentHeaderId.value
      };
      addHeaderRoelList(data).then(res => {
        if (res.code === 200) {
          filedSettingDialogVisible.value = false;
          ElMessage.success("字段屏蔽权限保存成功");
        }
      });
    })
    .catch(() => {
      // 取消
    });
};

// nextTick(() => {
//   const timeoutId = setTimeout(() => {
//     // 在这里放置 3 秒后执行的代码
//     // isLinkage.value = false; // 启用父子联动
//
//     // 执行完毕后销毁 setTimeout，避免它再次执行
//     clearTimeout(timeoutId); // 清除定时器
//   }, 500); // 延迟 3000 毫秒（即 3 秒）
// });

const handleSaveClick = async roleObj => {
  await handleSave(roleObj);
  emit("closeRoleMenu");
};
</script>

<template>
  <div
    v-if="isShow"
    class="h-full px-2 bg-bg_color overflow-auto"
    :class="[
      roleObj?.roleType === 'individual' ? '' : 'mt-2',
      deviceDetection() ? 'mt-2' : 'pb-2 ml-2 ',
      isShow ? '!min-w-[calc(100vw-60vw-268px)]' : 'w-full'
    ]"
  >
    <div class="flex justify-between px-3 pt-5 pb-4">
      <div class="flex">
        <span :class="iconClass">
          <IconifyIconOffline
            v-tippy="{
              content: '关闭'
            }"
            class="dark:text-white"
            width="18px"
            height="18px"
            :icon="Close"
            @click="handleClose"
          />
        </span>
        <span v-if="type === 0" :class="[iconClass, 'ml-2']">
          <IconifyIconOffline
            v-tippy="{
              content: '保存菜单权限'
            }"
            class="dark:text-white"
            width="18px"
            height="18px"
            :icon="Check"
            @click="handleSaveClick(roleObj)"
          />
        </span>
      </div>
      <p class="font-bold w-full truncate ml-20">
        {{ roleObj?.name }}
        {{ roleObj?.roleType === "individual" ? "的个人权限" : "菜单权限" }}
      </p>
    </div>
    <el-input
      v-model="treeSearchValue"
      placeholder="请输入菜单进行搜索"
      class="mb-1"
      clearable
      @input="onQueryChanged"
    />
    <div class="flex flex-wrap">
      <el-checkbox v-model="isExpandAll" label="展开/折叠" />
      <el-checkbox
        v-if="type === 0"
        v-model="isSelectAll"
        label="全选/全不选"
      />
      <el-checkbox v-if="type === 0" v-model="isLinkage" label="父子联动" />
    </div>
    <el-tree-v2
      v-if="isShow"
      ref="treeRef"
      :show-checkbox="type === 0"
      :data="type ? filedTreeData : treeData"
      :props="treeProps"
      :default-checked-keys="roleObj.sysMenuIds"
      :height="treeHeight"
      :check-strictly="!isLinkage"
      :filter-method="filterMethod"
      @node-click="nodeClick"
    >
      <template #default="{ node }">
        <span>{{ transformI18n(node.label) }}</span>
      </template>
    </el-tree-v2>

    <el-dialog v-model="filedSettingDialogVisible" :width="800" draggable>
      <el-checkbox
        v-model="checkAllFiled"
        :indeterminate="isIndeterminate"
        @change="handleCheckAllChange"
      >
        全选
      </el-checkbox>
      <el-checkbox-group
        v-model="checkedFiled"
        @change="handleCheckedCitiesChange"
      >
        <el-checkbox
          v-for="item in filedData"
          :key="item.field"
          style="width: 160px"
          :label="item.description"
          :value="item"
        />
      </el-checkbox-group>
      <div class="mt-4 flex justify-end">
        <el-button @click="filedSettingDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveFiled"> 保存 </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-dropdown-menu__item i) {
  margin: 0;
}

.main-content {
  margin: 24px 24px 0 !important;
}

.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style>
