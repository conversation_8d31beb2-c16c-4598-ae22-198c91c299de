<template>
  <div>
    <ReTable
      ref="tableRef"
      :mappingStatus="warehousePreparationMap"
      showTotal
      :getTotal="getTotal"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      :headerCellKeys="['materialDeliverySituation']"
      :tableRowWarnStatus="tableRowWarnStatus"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="tsx" setup>
import { useRoute } from "vue-router";
import { ref, h, reactive, watch, onMounted, nextTick } from "vue";
import { fullLifecycleWareHousePreparationId } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";

import {
  getWarehousePreparationTableAPI,
  getWarehousePreparationAPI,
  gettWarehousePreparationsTotalAPI,
  exporttWarehousePreparationAPI
} from "@/api/full-lifecycle/warehousePreparation";
import { useI18n } from "vue-i18n";
import { useFetchTableData, useParentColumnTag } from "@/utils/hooks";
import { warehousePreparationMap } from "../../utils";
import { useRouter } from "vue-router";
import { useNodeStore } from "@/store/modules/fullLifecycle";
const { setParentColumnTag } = useParentColumnTag();
const router = useRouter();
const { t } = useI18n(); // 解构出t方法

defineOptions({
  name: "WarehousePreparation"
});
const nodeStore = useNodeStore();
const route = useRoute();
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);

// 搜素内容
const queryData = ref({
  page: { current: 1, size: 20, default: 20 },
  query: [],
  order: [
    {
      name: "materialDeliverySituation",
      sort: "desc"
    },
    {
      name: "productionOrderState",
      sort: "asc"
    }
  ]
});

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  getTotalHook,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(
  fullLifecycleWareHousePreparationId,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息
getSortedColumnHeaders(getWarehousePreparationTableAPI, columns);

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(getWarehousePreparationAPI, {
  states: warehousePreparationMap,
  addPercentSigns: ["warehousingRate"]
});

const tableRowWarnStatus = reactive([
  {
    columnKey: "materialDeliverySituation",
    operator: "==",
    threshold: "未领料",
    className: "row-pending"
  },
  {
    columnKey: "materialDeliverySituation",
    operator: "==",
    threshold: "部分发料",
    className: "row-pending"
  }
]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exporttWarehousePreparationAPI, {
        queryId: nodeStore.queryId
      });
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);

// 需要求和字段数组
const sumList = ref([
  "productionOrderNumber",
  "endNumber",
  "outstandingQuantity",
  "inventoryOrderNumber",
  "unInventoryOrderNumber",
  "issueDecreasing"
]);

const getTotal = getTotalHook(sumList, gettWarehousePreparationsTotalAPI, {
  queryId: nodeStore.queryId
});

// 某行被点击
const handleRowClick = (row, column, cell) => {
  const columnList = ["productionOrderNo"];
  setParentColumnTag(row, columns.value, columnList, "DetailColumnFields");
  nodeStore.setSonPurchaseOrderProductionOrderNo(row.productionOrderNo);
  router.push({
    path: "/full-lifecycle/warehouse-preparation/detail"
  });
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
};
</script>
