<template>
  <div>
    <ReTable
      ref="tableRef"
      :mappingStatus="workshopProductionMap"
      showTotal
      :getTotal="getTotal"
      :topButtons="topButtons"
      :columns="columns"
      :fetchData="fetchTableData"
      :headerCellKeys="['productionOrderNo']"
      :tableRowWarnStatus="tableRowWarnStatus"
      :queryData="queryData"
      :updateQueryDate="updateQueryDate"
      @pageSizeChange="handlePageSizeChange"
      @currentPageChange="handleCurrentPageChange"
      @updateColumns="updateColumns"
      @selectedRows="handleSelectedRows"
      @rowClick="handleRowClick"
      @cellClick="handleCellClick"
      @InputSearchResetFilter="InputSearchResetFilter"
      @InputSearchUpdateFilter="InputSearchUpdateFilter"
    />
  </div>
</template>

<script lang="tsx" setup>
import { useRoute, useRouter } from "vue-router";
import { ref, reactive } from "vue";
import { fullLifecycleWorkShopProductionId } from "@/router/columnIdList";
import ReTable from "@/components/ReTable/Table.vue";
import {
  getWorkshopProductionAPI,
  getWorkshopProductionTableAPI,
  getWorkshopProductionsTotalAPI,
  exportWorkshopProductionAPI
} from "@/api/full-lifecycle/workshop-production/workshopProduction";

// 导入获取billNo相关hook
import { useFetchTableData } from "@/utils/hooks";
/** 表格状态数据 */
import { workshopProductionMap } from "@/views/full-lifecycle/utils";
import { useI18n } from "vue-i18n";
import { useNodeStore } from "@/store/modules/fullLifecycle";
import { useParentColumnTag } from "@/utils/hooks";
const { t } = useI18n(); // 解构出t方法

defineOptions({
  name: "WorkshopProduction"
});
const router = useRouter();
const nodeStore = useNodeStore();
const tableRef = ref(null);
const tableQuery = ref([]);
const selectRows = ref([]);
const { setParentColumnTag } = useParentColumnTag();

// 搜素内容
const queryData = ref({
  page: { current: 1, size: 20, default: 20 },
  query: [],
  order: [
    // {
    //   name: "finishRate",
    //   sort: "asc"
    // }
  ]
});

/**
 * @description 统一获取用于获取table数据的hook
 */
const {
  getFetchTableDataMethod,
  getSortedColumnHeaders,
  updateColumns,
  handleExportExcel,
  InputSearchUpdateFilter,
  InputSearchResetFilter,
  updateQueryDate,
  handleSelectedRows,
  getTotalHook,
  handleCurrentPageChange,
  handlePageSizeChange,
  columns
} = useFetchTableData(
  fullLifecycleWorkShopProductionId,
  tableRef,
  queryData,
  tableQuery,
  selectRows
);

//获取销售订单表头字段排序信息
getSortedColumnHeaders(getWorkshopProductionTableAPI, columns);

/**
 * @description 这里获取的是本页面的请求table数据函数
 */
const fetchTableData = getFetchTableDataMethod(getWorkshopProductionAPI, {
  states: workshopProductionMap
});

const tableRowWarnStatus = reactive([
  {
    columnKey: "completionColor",
    operator: "==",
    threshold: 1,
    className: "row-pending"
  }
]);

// 定义按钮配置
const topButtons = ref([
  {
    label: "buttons.pureExcelExport",
    type: "primary",
    size: "small",
    action: () => {
      handleExportExcel(exportWorkshopProductionAPI, {
        queryId: nodeStore.queryId
      });
    }
  },
  {
    label: "buttons.pureColumnSet",
    type: "primary",
    size: "small",
    action: () => {
      tableRef.value?.openColumnSetting();
    }
  }
]);

// 需要求和字段数组
const sumList = ref([
  "singlePrice",
  "outstandingQuantity",
  "completionNumber",
  "plannedCompletionNumber",
  "unqualifiedQuantitys",
  "ableSendWorkersNumbers",
  "scrapNumbers",
  "transferInQuantity",
  "singlePrice",
  "unInspectionQuantity",
  "reportedQuantity",
  "unreportedQuantity"
]);

// 获取求和函数
const getTotal = getTotalHook(sumList, getWorkshopProductionsTotalAPI, {
  queryId: nodeStore.queryId
});

// 某行被点击
const handleRowClick = (row, column, cell) => {
  // 筛选字段列表
  const columnList = ["productionOrderNo"]; // 客户指定的字段
  setParentColumnTag(row, columns.value, columnList, "filteredColumnFields");
  router.push({
    path: "/full-lifecycle/workshop-production/workshop-production-detail",
    query: {
      billNo: row.productionOrderNo
    }
  });
  console.log(row, column, cell, "某行被点击");
};

// 某单元格被点击
const handleCellClick = (row, column, cell, event) => {
  console.log(row, column, cell, event, "某单元格被点击");
};
</script>
